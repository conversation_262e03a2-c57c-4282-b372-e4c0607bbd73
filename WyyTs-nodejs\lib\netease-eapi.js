/**
 * Node.js版本的网易云EAPI完整实现
 * 支持无损音质解析，从Deno TypeScript版本转换而来
 */

import crypto from 'crypto';
import fetch from 'node-fetch';
import archiver from 'archiver';
import fs from 'fs-extra';
import path from 'path';

// MD5实现 - 使用Node.js内置crypto模块
class MD5 {
  static hash(str) {
    return crypto.createHash('md5').update(str, 'utf8').digest('hex');
  }
}

// 网易云EAPI类
export class NeteaseEAPI {
  constructor() {
    this.AES_KEY = Buffer.from('e82ckenh8dichen8', 'utf8');
    this.BASE_URL = 'https://interface3.music.163.com';
  }

  /**
   * 十六进制摘要
   */
  hexDigest(data) {
    return Array.from(data)
      .map(byte => byte.toString(16).padStart(2, '0'))
      .join('');
  }

  /**
   * MD5哈希摘要
   */
  hashDigest(text) {
    const hexString = MD5.hash(text);
    const bytes = Buffer.alloc(16);
    for (let i = 0; i < 16; i++) {
      bytes[i] = parseInt(hexString.substr(i * 2, 2), 16);
    }
    return bytes;
  }

  /**
   * MD5哈希十六进制摘要
   */
  hashHexDigest(text) {
    return MD5.hash(text);
  }

  /**
   * AES ECB加密 - 使用Node.js crypto模块
   */
  aesEncrypt(plaintext) {
    console.log('🔐 AES-ECB加密调试:');
    console.log('   原文长度:', plaintext.length);
    console.log('   密钥:', this.AES_KEY.toString('hex'));

    // 使用Node.js crypto进行AES-128-ECB加密
    const cipher = crypto.createCipher('aes-128-ecb', this.AES_KEY);
    cipher.setAutoPadding(true);
    
    let encrypted = cipher.update(plaintext, 'utf8');
    encrypted = Buffer.concat([encrypted, cipher.final()]);

    console.log('   ECB加密结果长度:', encrypted.length);
    console.log('   ECB加密结果前32字节:', encrypted.slice(0, 32).toString('hex'));

    return encrypted;
  }

  /**
   * 网易云EAPI请求 - 精确移植Python版本
   */
  async url_v1(songId, level, cookies) {
    const url = `${this.BASE_URL}/eapi/song/enhance/player/url/v1`;
    
    // 构建配置
    const config = {
      "os": "pc",
      "appver": "",
      "osver": "",
      "deviceId": "pyncm!",
      "requestId": Math.floor(Math.random() * (30000000 - 20000000 + 1) + 20000000).toString()
    };

    // 构建payload
    const payload = {
      'ids': [songId],
      'level': level,
      'encodeType': 'flac',
      'header': JSON.stringify(config)
    };

    // 沉浸环绕声特殊处理
    if (level === 'sky') {
      payload['immerseType'] = 'c51';
    }

    // 构建加密字符串
    const urlPath = '/api/song/enhance/player/url/v1';
    const payloadStr = JSON.stringify(payload);
    const digestText = `nobody${urlPath}use${payloadStr}md5forencrypt`;
    const digest = this.hashHexDigest(digestText);
    const paramsText = `${urlPath}-36cd479b6b5-${payloadStr}-36cd479b6b5-${digest}`;

    // AES加密
    const encrypted = this.aesEncrypt(paramsText);
    const encryptedHex = this.hexDigest(encrypted);

    // 构建Cookie字符串
    const cookieString = Object.entries(cookies)
      .map(([key, value]) => `${key}=${value}`)
      .join('; ');

    // 调试信息
    console.log('🔧 EAPI调试信息:');
    console.log('   URL:', url);
    console.log('   Payload:', payloadStr);
    console.log('   DigestText:', digestText);
    console.log('   Digest:', digest);
    console.log('   ParamsText:', paramsText);
    console.log('   EncryptedHex长度:', encryptedHex.length);
    console.log('   Cookie长度:', cookieString.length);

    // 发送请求
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.164 Safari/537.36',
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Content-Type': 'application/x-www-form-urlencoded',
        'Origin': 'https://music.163.com',
        'Referer': 'https://music.163.com/',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-site',
        'Cookie': cookieString
      },
      body: `params=${encryptedHex}`
    });

    console.log('📡 EAPI响应状态:', response.status);

    if (!response.ok) {
      throw new Error(`EAPI请求失败: ${response.status}`);
    }

    const responseText = await response.text();
    console.log('📡 EAPI响应内容:', responseText);

    if (!responseText || responseText.trim() === '') {
      throw new Error('EAPI返回空响应');
    }

    try {
      return JSON.parse(responseText);
    } catch (error) {
      throw new Error(`JSON解析失败: ${error.message}`);
    }
  }

  /**
   * 测试Cookie有效性
   */
  async testCookie(cookies) {
    const url = 'https://music.163.com/api/nuser/account/get';
    const cookieString = Object.entries(cookies)
      .map(([key, value]) => `${key}=${value}`)
      .join('; ');

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.164 Safari/537.36',
          'Referer': 'https://music.163.com/',
          'Content-Type': 'application/x-www-form-urlencoded',
          'Cookie': cookieString
        },
        body: ''
      });

      console.log('🍪 Cookie测试响应状态:', response.status);
      const text = await response.text();
      console.log('🍪 Cookie测试响应:', text.substring(0, 200));

      return response.ok && text.includes('account');
    } catch (error) {
      console.log('🍪 Cookie测试失败:', error.message);
      return false;
    }
  }

  /**
   * 获取歌曲详细信息
   */
  async getSongDetail(songId) {
    const url = 'https://interface3.music.163.com/api/v3/song/detail';
    const data = new URLSearchParams();
    data.append('c', JSON.stringify([{"id": parseInt(songId), "v": 0}]));

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Safari/537.36 Chrome/91.0.4472.164 NeteaseMusicDesktop/2.10.2.200154',
        'Referer': 'https://music.163.com/',
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: data
    });

    if (response.ok) {
      const result = await response.json();
      return result.songs?.[0] || null;
    }

    return null;
  }

  /**
   * 解析Cookie字符串
   */
  parseCookie(cookieString) {
    const cookies = {};
    cookieString.split(';').forEach(cookie => {
      const [key, value] = cookie.trim().split('=');
      if (key && value) {
        cookies[key] = value;
      }
    });
    return cookies;
  }

  /**
   * 创建完整的Cookie对象
   */
  createFullCookieObject(parsedCookies) {
    return {
      "os": "pc",
      "appver": "",
      "osver": "",
      "deviceId": "pyncm!",
      ...parsedCookies
    };
  }

  /**
   * 获取音质等级的中文名称
   */
  getQualityName(level) {
    const qualityNames = {
      'standard': '标准音质',
      'exhigh': '极高音质', 
      'lossless': '无损音质',
      'hires': 'Hi-Res音质',
      'sky': '沉浸环绕声',
      'jyeffect': '高清环绕声',
      'jymaster': '超清母带'
    };
    return qualityNames[level] || '未知音质';
  }

  /**
   * 格式化文件大小
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '未知大小';
    return `${(bytes / 1024 / 1024).toFixed(2)}MB`;
  }

  /**
   * 检查是否为无损音质
   */
  isLosslessQuality(br) {
    return br >= 900000;
  }

  /**
   * 获取比特率对应的音质等级
   */
  getBitrateQuality(br) {
    if (br >= 1999000) return 'Hi-Res/超清母带';
    if (br >= 999000) return '无损音质';
    if (br >= 320000) return '极高音质';
    if (br >= 128000) return '标准音质';
    return '低音质';
  }

  /**
   * 搜索歌曲
   */
  async searchSongs(keyword, limit = 30) {
    const url = 'https://interface3.music.163.com/api/search/get/web';
    const data = new URLSearchParams();
    data.append('s', keyword);
    data.append('type', '1'); // 1=歌曲
    data.append('offset', '0');
    data.append('total', 'true');
    data.append('limit', limit.toString());

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Safari/537.36 Chrome/91.0.4472.164 NeteaseMusicDesktop/2.10.2.200154',
        'Referer': 'https://music.163.com/',
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: data
    });

    if (!response.ok) {
      throw new Error(`搜索请求失败: ${response.status}`);
    }

    const result = await response.json();
    return result.result || { songs: [], songCount: 0 };
  }

  /**
   * 获取用户信息
   */
  async getUserInfo(cookies) {
    const url = 'https://music.163.com/api/nuser/account/get';
    const cookieString = Object.entries(cookies)
      .map(([key, value]) => `${key}=${value}`)
      .join('; ');

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.164 Safari/537.36',
          'Referer': 'https://music.163.com/',
          'Content-Type': 'application/x-www-form-urlencoded',
          'Cookie': cookieString
        },
        body: ''
      });

      if (response.ok) {
        const result = await response.json();
        return result;
      }
      return null;
    } catch (error) {
      console.error('获取用户信息失败:', error);
      return null;
    }
  }

  /**
   * 获取专辑详情
   */
  async getAlbumDetail(albumId) {
    console.log(`📀 获取专辑详情: ID=${albumId}`);

    try {
      // 使用新的专辑API
      const url = `https://music.163.com/api/v1/album/${albumId}`;

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Safari/537.36 Chrome/91.0.4472.164 NeteaseMusicDesktop/2.10.2.200154',
          'Referer': 'https://music.163.com/',
          'Accept': 'application/json, text/plain, */*',
          'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
        }
      });

      if (!response.ok) {
        console.warn(`专辑API响应失败: ${response.status}, 尝试备用API...`);
        return await this.getAlbumDetailBackup(albumId);
      }

      const result = await response.json();
      console.log(`📀 专辑API响应:`, result);

      // 检查数据结构
      if (result.songs && result.songs.length > 0) {
        console.log(`📀 专辑《${result.album?.name || '未知专辑'}》获取成功，包含 ${result.songs.length} 首歌曲`);
        return {
          ...result.album,
          songs: result.songs
        };
      } else if (result.album && result.album.songs && result.album.songs.length > 0) {
        console.log(`📀 专辑《${result.album.name}》获取成功，包含 ${result.album.songs.length} 首歌曲`);
        return result.album;
      } else {
        console.warn(`专辑数据格式异常，songs字段为空或不存在，尝试备用API...`);
        return await this.getAlbumDetailBackup(albumId);
      }
    } catch (error) {
      console.error(`获取专辑详情异常: ${error.message}，尝试备用API...`);
      return await this.getAlbumDetailBackup(albumId);
    }
  }

  /**
   * 备用专辑详情获取方法
   */
  async getAlbumDetailBackup(albumId) {
    console.log(`📀 使用备用API获取专辑详情: ID=${albumId}`);

    try {
      // 使用EAPI方式获取专辑详情
      const url = `${this.BASE_URL}/eapi/v1/album/${albumId}`;
      const params = {
        id: albumId
      };

      const apiPath = `/api/v1/album/${albumId}`;
      const digestText = `nobody${apiPath}use${JSON.stringify(params)}md5forencrypt`;
      const digest = this.hashHexDigest(digestText);

      const paramsText = `${apiPath}-36cd479b6b5-${JSON.stringify(params)}-36cd479b6b5-${digest}`;
      const encryptedParams = this.aesEncrypt(paramsText);
      const encryptedHex = this.hexDigest(encryptedParams);

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Safari/537.36 Chrome/91.0.4472.164 NeteaseMusicDesktop/2.10.2.200154',
          'Referer': 'https://music.163.com/',
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: `params=${encryptedHex}`
      });

      if (!response.ok) {
        throw new Error(`备用API响应失败: ${response.status}`);
      }

      const result = await response.json();
      console.log(`📀 备用API响应:`, result);

      // 检查备用API的数据结构
      if (result.songs && result.songs.length > 0) {
        console.log(`📀 专辑《${result.album?.name || '未知专辑'}》获取成功（备用API），包含 ${result.songs.length} 首歌曲`);
        return {
          ...result.album,
          songs: result.songs
        };
      } else if (result.album && result.album.songs && result.album.songs.length > 0) {
        console.log(`📀 专辑《${result.album.name}》获取成功（备用API），包含 ${result.album.songs.length} 首歌曲`);
        return result.album;
      } else {
        throw new Error('备用API也无法获取专辑信息');
      }
    } catch (error) {
      console.error(`备用API获取专辑详情失败: ${error.message}`);
      throw new Error(`无法获取专辑信息: ${error.message}`);
    }
  }

  /**
   * 获取歌单详情
   */
  async getPlaylistDetail(playlistId, cookies) {
    console.log(`📋 获取歌单详情: ID=${playlistId}`);

    try {
      // 首先获取基本信息
      const url = `${this.BASE_URL}/eapi/v6/playlist/detail`;
      const params = {
        id: playlistId,
        n: '3000',
        s: '8'
      };

      const apiPath = '/api/v6/playlist/detail';
      const digestText = `nobody${apiPath}use${JSON.stringify(params)}md5forencrypt`;
      const digest = this.hashHexDigest(digestText);

      const paramsText = `${apiPath}-36cd479b6b5-${JSON.stringify(params)}-36cd479b6b5-${digest}`;
      const encryptedParams = this.aesEncrypt(paramsText);
      const encryptedHex = this.hexDigest(encryptedParams);

      const cookieString = cookies ?
        Object.entries(cookies).map(([key, value]) => `${key}=${value}`).join('; ') :
        '';

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Safari/537.36 Chrome/91.0.4472.164 NeteaseMusicDesktop/2.10.2.200154',
          'Referer': 'https://music.163.com/',
          'Content-Type': 'application/x-www-form-urlencoded',
          'Cookie': cookieString
        },
        body: `params=${encryptedHex}`
      });

      if (!response.ok) {
        throw new Error(`获取歌单详情失败: ${response.status}`);
      }

      const result = await response.json();
      if (result.code === 200 && result.playlist) {
        const playlist = result.playlist;

        // 如果提供了cookies，尝试获取完整歌曲列表
        if (cookies) {
          try {
            const completeTracks = await this.getCompletePlaylistTracks(playlistId, cookies);
            playlist.tracks = completeTracks;
            console.log(`📋 歌单详情获取成功: ${playlist.name}, 完整歌曲数量: ${completeTracks.length}`);
          } catch (error) {
            console.warn(`获取完整歌单失败，使用基本信息: ${error.message}`);
            console.log(`📋 歌单详情获取成功: ${playlist.name}, 基本歌曲数量: ${playlist.tracks?.length || 0} (可能受限)`);
          }
        } else {
          console.log(`📋 歌单详情获取成功: ${playlist.name}, 基本歌曲数量: ${playlist.tracks?.length || 0} (未提供cookies，可能受限)`);
        }

        return playlist;
      } else {
        throw new Error(`获取歌单详情失败: ${result.message || '未知错误'}`);
      }
    } catch (error) {
      console.error(`❌ 获取歌单详情异常:`, error);
      throw error;
    }
  }

  /**
   * 获取完整歌单歌曲列表 - 解决10首限制问题
   */
  async getCompletePlaylistTracks(playlistId, cookies) {
    console.log(`📋 开始获取完整歌单: ID=${playlistId}`);

    try {
      // 首先尝试使用Web API获取完整歌单
      const url = `https://music.163.com/api/v6/playlist/detail`;
      const params = new URLSearchParams({
        id: playlistId,
        n: '30000',  // 获取30000首歌曲
        s: '8'
      });

      const cookieString = Object.entries(cookies)
        .map(([key, value]) => `${key}=${value}`)
        .join('; ');

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Safari/537.36 Chrome/91.0.4472.164 NeteaseMusicDesktop/2.10.2.200154',
          'Referer': 'https://music.163.com/',
          'Content-Type': 'application/x-www-form-urlencoded',
          'Cookie': cookieString
        },
        body: params.toString()
      });

      if (response.ok) {
        const result = await response.json();

        if (result.code === 200 && result.playlist && result.playlist.tracks) {
          const tracks = result.playlist.tracks;
          console.log(`📋 Web API获取歌单成功: 获取到 ${tracks.length} 首歌曲`);

          // 如果获取到的歌曲数量较少，尝试分页获取
          if (tracks.length < 50) {
            console.log(`📋 歌曲数量为 ${tracks.length}，可能受到限制，尝试分页获取完整歌单...`);
            return await this.getPlaylistTracksByPagination(playlistId, cookies);
          }

          return tracks;
        }
      }

      // 如果Web API失败，回退到分页获取
      console.log(`📋 Web API获取失败，尝试分页获取...`);
      return await this.getPlaylistTracksByPagination(playlistId, cookies);

    } catch (error) {
      console.error(`获取完整歌单异常: ${error.message}`);
      // 回退到分页获取
      return await this.getPlaylistTracksByPagination(playlistId, cookies);
    }
  }

  /**
   * 通过分页方式获取歌单歌曲
   */
  async getPlaylistTracksByPagination(playlistId, cookies) {
    console.log(`📋 开始分页获取歌单: ID=${playlistId}`);

    const allTracks = [];
    let offset = 0;
    const limit = 1000; // 每次获取1000首
    let attempts = 0;
    const maxAttempts = 6; // 最多尝试6次

    while (attempts < maxAttempts) {
      try {
        const url = `https://music.163.com/api/v6/playlist/detail`;
        const params = new URLSearchParams({
          id: playlistId,
          n: limit.toString(),
          s: offset.toString(),
          t: '0',
          total: 'true'
        });

        const cookieString = Object.entries(cookies)
          .map(([key, value]) => `${key}=${value}`)
          .join('; ');

        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Safari/537.36 Chrome/91.0.4472.164 NeteaseMusicDesktop/2.10.2.200154',
            'Referer': 'https://music.163.com/',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Cookie': cookieString
          },
          body: params.toString()
        });

        if (!response.ok) {
          console.warn(`分页获取失败: ${response.status}`);
          break;
        }

        const result = await response.json();

        if (result.code === 200 && result.playlist && result.playlist.tracks) {
          const tracks = result.playlist.tracks;

          if (tracks.length === 0) {
            console.log(`📋 分页获取完成: 没有更多歌曲`);
            break;
          }

          // 过滤重复歌曲
          const newTracks = tracks.filter(track =>
            !allTracks.some(existing => existing.id === track.id)
          );

          allTracks.push(...newTracks);

          console.log(`📋 分页获取进度: offset=${offset}, 本次获取=${tracks.length}首, 新增=${newTracks.length}首, 总计=${allTracks.length}首`);

          // 如果本次获取的歌曲数量少于limit，说明已经获取完毕
          if (tracks.length < limit) {
            break;
          }

          offset += limit;
        } else {
          console.warn(`分页获取失败: ${result.message || '未知错误'}`);
          break;
        }
      } catch (error) {
        console.error(`分页获取异常: ${error.message}`);
        break;
      }

      attempts++;
    }

    console.log(`✅ 分页获取完成: 总计 ${allTracks.length} 首歌曲`);
    return allTracks;
  }

  /**
   * 获取专辑信息（用于歌单选择界面）
   */
  async getAlbumInfo(albumId, cookies) {
    const albumDetail = await this.getAlbumDetail(albumId);

    if (!albumDetail || !albumDetail.songs) {
      throw new Error('无法获取专辑信息');
    }

    return {
      name: albumDetail.name,
      songs: albumDetail.songs.map(song => ({
        id: song.id,
        name: song.name,
        artist: (song.artists || song.ar || []).map(a => a.name).join('/') || '未知艺术家'
      }))
    };
  }

  /**
   * 获取歌单信息（用于歌单选择界面）
   */
  async getPlaylistInfo(playlistId, cookies) {
    const playlistDetail = await this.getPlaylistDetail(playlistId, cookies);

    if (!playlistDetail || !playlistDetail.tracks) {
      throw new Error('无法获取歌单信息');
    }

    return {
      name: playlistDetail.name,
      songs: playlistDetail.tracks.map(track => ({
        id: track.id,
        name: track.name,
        artist: track.ar.map(a => a.name).join('/')
      }))
    };
  }

  /**
   * 批量下载歌曲
   */
  async batchDownload(songIds, level, cookies, concurrency = 3) {
    const results = [];
    let successCount = 0;
    let failedCount = 0;

    // 分批处理，控制并发数
    for (let i = 0; i < songIds.length; i += concurrency) {
      const batch = songIds.slice(i, i + concurrency);
      const batchPromises = batch.map(async (songId) => {
        try {
          const result = await this.url_v1(songId, level, cookies);

          if (result && result.data && result.data.length > 0) {
            const songData = result.data[0];

            if (songData.url) {
              // 获取歌曲详情
              const songInfo = await this.getSongDetail(songId);

              const downloadResult = {
                id: parseInt(songId),
                name: songInfo?.name || `歌曲ID: ${songId}`,
                artist: songInfo?.ar?.map(a => a.name).join('/') || '未知歌手',
                success: true,
                url: songData.url.replace('http://', 'https://'),
                size: songData.size,
                br: songData.br
              };

              successCount++;
              return downloadResult;
            } else {
              throw new Error(`无法获取下载链接 (code: ${songData.code})`);
            }
          } else {
            throw new Error('无响应数据');
          }
        } catch (error) {
          const songInfo = await this.getSongDetail(songId).catch(() => null);

          const downloadResult = {
            id: parseInt(songId),
            name: songInfo?.name || `歌曲ID: ${songId}`,
            artist: songInfo?.ar?.map(a => a.name).join('/') || '未知歌手',
            success: false,
            error: error.message
          };

          failedCount++;
          return downloadResult;
        }
      });

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);

      // 显示进度
      console.log(`📥 批量下载进度: ${results.length}/${songIds.length} (成功: ${successCount}, 失败: ${failedCount})`);
    }

    return {
      total: songIds.length,
      success: successCount,
      failed: failedCount,
      results
    };
  }

  /**
   * 批量下载选中的歌曲
   */
  async batchDownloadSongs(songs, level, cookies) {
    const results = [];
    let successCount = 0;
    let failedCount = 0;

    // 处理每首歌曲
    for (const song of songs) {
      try {
        const result = await this.url_v1(song.id.toString(), level, cookies);

        if (result && result.data && result.data.length > 0) {
          const songData = result.data[0];

          if (songData.url) {
            const downloadResult = {
              id: song.id,
              name: song.name,
              artist: song.artist,
              success: true,
              url: songData.url.replace('http://', 'https://'),
              size: songData.size,
              br: songData.br
            };

            successCount++;
            results.push(downloadResult);
          } else {
            throw new Error(`无法获取下载链接 (code: ${songData.code})`);
          }
        } else {
          throw new Error('无响应数据');
        }
      } catch (error) {
        const downloadResult = {
          id: song.id,
          name: song.name,
          artist: song.artist,
          success: false,
          error: error.message
        };

        failedCount++;
        results.push(downloadResult);
      }

      // 显示进度
      console.log(`📥 下载进度: ${results.length}/${songs.length} (成功: ${successCount}, 失败: ${failedCount})`);
    }

    // 根据歌曲数量决定下载方式
    if (songs.length > 30) {
      // 超过30首歌曲，生成ZIP下载包
      try {
        const zipResult = await this.generateZipDownload(results.filter(r => r.success), level);
        return {
          status: 200,
          downloadType: 'zip',
          success: successCount,
          failed: failedCount,
          zipUrl: zipResult.zipUrl,
          zipFilename: zipResult.zipFilename
        };
      } catch (error) {
        console.error('ZIP生成失败，回退到单独下载:', error.message);
        return {
          status: 200,
          downloadType: 'individual',
          success: successCount,
          failed: failedCount,
          results
        };
      }
    } else {
      // 30首及以下，单独下载
      return {
        status: 200,
        downloadType: 'individual',
        success: successCount,
        failed: failedCount,
        results
      };
    }
  }

  /**
   * 生成ZIP下载包
   */
  async generateZipDownload(successResults, level) {
    console.log(`📦 开始生成ZIP下载包，包含 ${successResults.length} 首歌曲`);

    // 创建下载目录
    const downloadDir = './public/downloads';
    await fs.ensureDir(downloadDir);

    const zipFilename = `网易云音乐批量下载_${new Date().toISOString().slice(0, 10)}_${Date.now()}.zip`;
    const zipPath = path.join(downloadDir, zipFilename);

    // 创建临时目录存放音乐文件
    const tempDir = await fs.mkdtemp(path.join(require('os').tmpdir(), 'netease_music_'));

    try {
      // 下载所有音乐文件到临时目录
      const downloadPromises = successResults.map(async (result, index) => {
        try {
          if (!result.url) {
            throw new Error('缺少下载链接');
          }

          const fileExtension = level === 'lossless' || level === 'hires' ? 'flac' : 'mp3';
          const safeFilename = this.sanitizeFilename(`${result.artist} - ${result.name}.${fileExtension}`);
          const filepath = path.join(tempDir, safeFilename);

          console.log(`📥 下载音乐文件 ${index + 1}/${successResults.length}: ${safeFilename}`);

          // 下载音乐文件
          const musicResponse = await fetch(result.url);
          if (!musicResponse.ok) {
            throw new Error(`下载失败: ${musicResponse.status}`);
          }

          const musicBuffer = await musicResponse.buffer();
          await fs.writeFile(filepath, musicBuffer);

          console.log(`✅ 下载完成: ${safeFilename} (${(musicBuffer.length / 1024 / 1024).toFixed(2)}MB)`);
          return { success: true, filename: safeFilename, filepath };
        } catch (error) {
          console.error(`❌ 下载失败: ${result.name} - ${error.message}`);

          // 创建错误说明文件
          const errorFilename = this.sanitizeFilename(`${result.artist} - ${result.name} - 下载失败.txt`);
          const errorFilepath = path.join(tempDir, errorFilename);
          const errorContent = `下载失败: ${result.name}\n歌手: ${result.artist}\n错误: ${error.message}\n时间: ${new Date().toLocaleString()}`;
          await fs.writeFile(errorFilepath, errorContent, 'utf8');

          return { success: false, filename: errorFilename, filepath: errorFilepath, error: error.message };
        }
      });

      const downloadResults = await Promise.all(downloadPromises);
      const successfulDownloads = downloadResults.filter(r => r.success);

      console.log(`📦 音乐文件下载完成: 成功 ${successfulDownloads.length}/${successResults.length}`);

      // 创建ZIP文件
      await this.createZipFile(tempDir, zipPath);

      // 生成下载URL
      const zipUrl = `/downloads/${zipFilename}`;

      console.log(`✅ ZIP文件生成成功: ${zipFilename}`);

      return { zipUrl, zipFilename };

    } finally {
      // 清理临时目录
      try {
        await fs.remove(tempDir);
        console.log(`🧹 临时目录清理完成: ${tempDir}`);
      } catch (error) {
        console.warn(`⚠️ 临时目录清理失败: ${error.message}`);
      }
    }
  }

  /**
   * 创建ZIP文件 - 使用archiver库
   */
  async createZipFile(sourceDir, zipPath) {
    console.log(`📦 开始创建ZIP文件: ${zipPath}`);

    return new Promise((resolve, reject) => {
      // 创建文件输出流
      const output = fs.createWriteStream(zipPath);
      const archive = archiver('zip', {
        zlib: { level: 9 } // 设置压缩级别
      });

      // 监听所有archive数据已写入完成
      output.on('close', () => {
        console.log(`📦 ZIP文件创建成功: ${zipPath} (${(archive.pointer() / 1024 / 1024).toFixed(2)}MB)`);
        resolve();
      });

      // 监听错误
      archive.on('error', (err) => {
        reject(new Error(`ZIP创建失败: ${err.message}`));
      });

      // 将archive的输出流连接到文件
      archive.pipe(output);

      // 添加整个目录到ZIP
      archive.directory(sourceDir, false);

      // 完成archive
      archive.finalize();
    });
  }

  /**
   * 清理文件名中的非法字符
   */
  sanitizeFilename(filename) {
    // 移除或替换Windows文件名中的非法字符
    return filename
      .replace(/[<>:"/\\|?*]/g, '_')  // 替换非法字符为下划线
      .replace(/\s+/g, ' ')           // 合并多个空格
      .trim()                         // 移除首尾空格
      .substring(0, 200);             // 限制文件名长度
  }
}
