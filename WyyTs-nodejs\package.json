{"name": "wyyts-nodejs", "version": "1.0.0", "description": "网易云音乐解析器 - Node.js版本", "main": "api/index.js", "scripts": {"dev": "vercel dev", "build": "echo 'Static files ready in public directory'", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["netease", "music", "parser", "download", "nodejs"], "author": "Claude 4.0 sonnet", "license": "MIT", "dependencies": {"dotenv": "^16.3.1", "node-fetch": "^3.3.2"}, "devDependencies": {"@vercel/node": "^3.0.0"}, "engines": {"node": ">=16.0.0"}}