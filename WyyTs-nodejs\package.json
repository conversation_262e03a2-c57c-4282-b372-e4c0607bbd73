{"name": "wyyts-nodejs", "version": "1.0.0", "description": "网易云音乐解析器 - Node.js版本", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["netease", "music", "parser", "download", "nodejs"], "author": "Claude 4.0 sonnet", "license": "MIT", "dependencies": {"archiver": "^6.0.1", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^17.0.1", "express": "^4.18.2", "fs-extra": "^11.1.1", "node-fetch": "^3.3.2", "path": "^0.12.7"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=16.0.0"}}