// 网易云音乐解析器 - Node.js版服务器
// 从Deno版本转换而来的现代化模块架构

import express from 'express';
import cors from 'cors';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs-extra';
import { NeteaseEAPI } from './lib/netease-eapi.js';

// ES模块中获取__dirname
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 环境检测
const isProduction = process.env.NODE_ENV === 'production';

// 配置管理
class Config {
  constructor() {
    this.NETEASE_COOKIE = process.env.NETEASE_COOKIE || '';
    this.PORT = parseInt(process.env.PORT || (isProduction ? '8000' : '3004'));
    this.DEBUG = (process.env.DEBUG || 'false').toLowerCase() === 'true';
    this.SEARCH_LIMIT = parseInt(process.env.SEARCH_LIMIT || '50');
    this.DOWNLOAD_CONCURRENCY = parseInt(process.env.DOWNLOAD_CONCURRENCY || '3');
    
    this.validateConfig();
  }

  validateConfig() {
    // 验证Cookie - 但不在启动时抛出错误
    if (!this.NETEASE_COOKIE || !this.NETEASE_COOKIE.includes('MUSIC_U=')) {
      console.warn('⚠️ 警告：未配置有效的NETEASE_COOKIE环境变量');
      if (isProduction) {
        console.log('🌐 请在生产环境设置环境变量 NETEASE_COOKIE');
      } else {
        console.log('💻 请设置环境变量：export NETEASE_COOKIE="your_cookie"');
      }
      console.log('🔧 Cookie格式示例：MUSIC_U=your_music_u_value;os=pc;appver=8.9.75;');
      console.log('📝 服务将启动，但API调用将返回错误直到设置有效Cookie');
    }
  }
}

const config = new Config();
const api = new NeteaseEAPI();
const app = express();

// 中间件配置
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'OPTIONS'],
  allowedHeaders: ['Content-Type']
}));

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 静态文件服务
app.use(express.static(path.join(__dirname, 'public')));

// 短链接解析
async function resolveShortLink(shortCode) {
  try {
    // 构造短链接URL
    const shortUrl = `http://163cn.tv/${shortCode}`;
    
    // 发送HEAD请求获取重定向
    const response = await fetch(shortUrl, {
      method: 'HEAD',
      redirect: 'manual'
    });

    const location = response.headers.get('location');
    if (location) {
      // 从重定向URL中提取歌曲ID
      const match = location.match(/id=(\d+)/);
      if (match) {
        return match[1];
      }
    }

    return null;
  } catch (error) {
    console.error('短链接解析失败:', error);
    return null;
  }
}

// API路由

// 单曲解析API
app.post('/api/song', async (req, res) => {
  try {
    let { ids, url: songUrl, level = 'lossless' } = req.body;
    let songId = ids || songUrl;

    console.log('🎵 收到单曲解析请求:', { songId, level });

    if (!songId) {
      return res.status(400).json({
        status: 400,
        error: '必须提供歌曲ID或短链接'
      });
    }

    // 如果是短链接代码，先解析
    if (typeof songId === 'string' && songId.length < 10 && !/^\d+$/.test(songId)) {
      console.log(`🔗 解析短链接: ${songId}`);
      const resolvedId = await resolveShortLink(songId);
      if (resolvedId) {
        songId = resolvedId;
        console.log(`✅ 短链接解析成功: ${songId}`);
      } else {
        return res.status(400).json({
          status: 400,
          error: '短链接解析失败'
        });
      }
    }

    console.log(`🎵 解析歌曲: ${songId}, 音质: ${level}`);

    // 检查Cookie是否有效
    if (!config.NETEASE_COOKIE || !config.NETEASE_COOKIE.includes('MUSIC_U=')) {
      return res.status(400).json({
        status: 400,
        error: '服务器未配置有效的NETEASE_COOKIE环境变量，请联系管理员设置'
      });
    }

    const parsedCookies = api.parseCookie(config.NETEASE_COOKIE);
    const cookies = api.createFullCookieObject(parsedCookies);

    // 测试Cookie有效性
    console.log('🍪 测试Cookie有效性...');
    const cookieValid = await api.testCookie(cookies);
    console.log('🍪 Cookie测试结果:', cookieValid ? '✅ 有效' : '❌ 无效');

    const result = await api.url_v1(songId, level, cookies);

    if (!result?.data?.[0]) {
      return res.status(400).json({
        status: 400,
        error: '无法获取歌曲信息'
      });
    }

    const songData = result.data[0];

    if (!songData.url) {
      return res.status(400).json({
        status: 400,
        error: '无法获取歌曲URL，可能是版权限制或需要会员权限'
      });
    }

    // 获取歌曲详情
    let songInfo = {};
    try {
      songInfo = await api.getSongDetail(songId);
    } catch (error) {
      console.warn('获取歌曲详情失败:', error.message);
    }

    const responseData = {
      status: 200,
      name: songInfo?.name || `歌曲ID: ${songId}`,
      ar_name: songInfo?.ar?.map(artist => artist.name).join('/') || '网易云音乐',
      al_name: songInfo?.al?.name || '专辑信息',
      level: api.getQualityName(level),
      size: api.formatFileSize(songData.size),
      url: songData.url.replace('http://', 'https://'),
      br: songData.br,
      pic: songInfo?.al?.picUrl || '',
      debug: {
        requestedLevel: level,
        actualBr: songData.br,
        isLossless: api.isLosslessQuality(songData.br),
        environment: isProduction ? 'Production' : 'Development'
      }
    };

    if (level === 'lossless' && api.isLosslessQuality(songData.br)) {
      responseData.note = `🎉 成功获取无损音质! 比特率: ${songData.br}`;
    } else if (level === 'lossless') {
      responseData.note = `⚠️ 请求无损但获取到较低音质，比特率: ${songData.br || '未知'}`;
    }

    res.json(responseData);

  } catch (error) {
    console.error('单曲解析错误:', error);
    res.status(500).json({
      status: 500,
      error: '服务器内部错误'
    });
  }
});

// 歌单/专辑信息API
app.post('/api/playlist-info', async (req, res) => {
  try {
    let { type, id, url, token } = req.body;

    console.log('📋 收到请求体:', req.body);

    // 支持落雪音乐的URL###TOKEN格式（兼容处理）
    if (url && url.includes('###')) {
      const [urlPart, tokenPart] = url.split('###');
      url = urlPart.trim();
      token = tokenPart.trim();
      console.log('🔑 检测到落雪音乐URL###TOKEN格式，token长度:', token.length);

      // 从URL中提取ID
      const patterns = [
        /playlist\?id=(\d+)/,           // 标准格式
        /m\/playlist\?id=(\d+)/,        // 移动端格式
        /playlist\/(\d+)/               // 简化格式
      ];

      for (const pattern of patterns) {
        const match = url.match(pattern);
        if (match) {
          id = match[1];
          type = 'playlist';
          break;
        }
      }

      // 如果是纯数字，直接作为歌单ID
      if (!id && /^\d+$/.test(url)) {
        id = url;
        type = 'playlist';
      }
    }

    // 如果前端直接传递了token参数（新的处理方式）
    if (token) {
      console.log('🔑 检测到前端传递的token参数，token长度:', token.length);
    }

    console.log(`📋 获取歌单信息: 类型=${type}, ID=${id}, 有token=${!!token}`);

    // 构建cookies - 区分普通歌单和私人歌单
    let cookies;

    if (token) {
      // 使用用户提供的token (适用于"我喜欢的歌单"等私人歌单)
      cookies = {
        MUSIC_U: token,
        os: 'pc',
        appver: '8.9.75'
      };
      console.log('🍪 使用用户提供的token (私人歌单)');
    } else {
      // 普通歌单：优先使用环境变量Cookie，如果没有则尝试无Cookie访问
      if (config.NETEASE_COOKIE && config.NETEASE_COOKIE.includes('MUSIC_U=')) {
        const parsedCookies = api.parseCookie(config.NETEASE_COOKIE);
        cookies = api.createFullCookieObject(parsedCookies);
        console.log('🍪 使用环境变量cookie (普通歌单)');
      } else {
        // 对于公开歌单，尝试无Cookie访问
        cookies = {
          os: 'pc',
          appver: '8.9.75'
        };
        console.log('🍪 使用基础cookie (公开歌单)');
      }
    }

    let result;

    if (type === 'album') {
      result = await api.getAlbumInfo(id, cookies);
    } else if (type === 'playlist') {
      result = await api.getPlaylistInfo(id, cookies);
    } else {
      throw new Error('无效的类型参数');
    }

    res.json({
      status: 200,
      ...result
    });

  } catch (error) {
    console.error('获取歌单信息错误:', error);
    res.status(500).json({
      status: 500,
      error: `获取歌单信息失败: ${error.message}`
    });
  }
});

// Cookie检查API
app.post('/api/cookie-check', async (req, res) => {
  try {
    console.log('🍪 检测Cookie和会员状态');

    // 检查Cookie是否有效
    if (!config.NETEASE_COOKIE || !config.NETEASE_COOKIE.includes('MUSIC_U=')) {
      return res.json({
        status: 200,
        valid: false,
        error: '服务器未配置有效的NETEASE_COOKIE环境变量'
      });
    }

    const parsedCookies = api.parseCookie(config.NETEASE_COOKIE);
    const cookies = api.createFullCookieObject(parsedCookies);

    const isValid = await api.testCookie(cookies);

    if (!isValid) {
      return res.json({
        status: 200,
        valid: false,
        error: 'Cookie已失效'
      });
    }

    // 获取用户信息和会员状态
    const userInfo = await api.getUserInfo(cookies);
    const profile = userInfo?.profile || {};
    const vipInfo = profile.vipType || 0;

    // 尝试多个可能的VIP过期时间字段
    let vipExpireTime = 0;
    if (profile.vipExpireTime && profile.vipExpireTime > 0) {
      vipExpireTime = profile.vipExpireTime;
    } else if (profile.viptypeVersion && profile.viptypeVersion > 0) {
      vipExpireTime = profile.viptypeVersion;
    }

    // 计算会员剩余天数
    const now = Date.now();
    let vipDaysLeft = 0;
    let vipExpired = true;

    if (vipExpireTime > 0) {
      vipDaysLeft = vipExpireTime > now ? Math.ceil((vipExpireTime - now) / (1000 * 60 * 60 * 24)) : 0;
      vipExpired = vipExpireTime <= now;
    }

    // 智能判断VIP状态
    const isVip = vipInfo > 0;
    if (!isVip) {
      // 非会员用户
    } else if (vipExpireTime === 0 || vipInfo >= 11) {
      // SVIP用户或长期有效
      vipExpired = false;
      vipDaysLeft = 999;
    }

    res.json({
      status: 200,
      valid: true,
      vipType: vipInfo,
      vipExpired: vipExpired,
      vipDaysLeft: vipDaysLeft,
      vipExpireTime: vipExpireTime > 0 ? new Date(vipExpireTime).toLocaleDateString('zh-CN') : '长期有效',
      isVip: isVip
    });

  } catch (error) {
    console.error('Cookie检测失败:', error);
    res.status(500).json({
      status: 500,
      valid: false,
      error: error.message
    });
  }
});

// 服务状态API
app.get('/api/status', (req, res) => {
  const hasValidCookie = config.NETEASE_COOKIE && config.NETEASE_COOKIE.includes('MUSIC_U=');

  res.json({
    status: 200,
    service: '网易云音乐解析器',
    version: 'Node.js版',
    environment: isProduction ? 'Production' : 'Development',
    cookieConfigured: hasValidCookie,
    message: hasValidCookie ? '服务正常运行' : '需要配置NETEASE_COOKIE环境变量',
    configHelp: {
      environment: '环境变量设置',
      localEnvironment: 'export NETEASE_COOKIE="your_cookie"',
      cookieFormat: 'MUSIC_U=your_music_u_value;os=pc;appver=8.9.75;'
    }
  });
});

// 根路径处理
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// 404处理
app.use((req, res) => {
  res.status(404).json({ error: 'Not Found' });
});

// 错误处理中间件
app.use((err, req, res, next) => {
  console.error('服务器错误:', err);
  res.status(500).json({
    status: 500,
    error: '服务器内部错误'
  });
});

// 启动服务器
app.listen(config.PORT, () => {
  console.log(`🚀 Node.js 偷听启动`);
  console.log(`🌐 环境: ${isProduction ? 'Production' : 'Development'}`);
  console.log(`🔍 Cookie状态: ${config.NETEASE_COOKIE && config.NETEASE_COOKIE.includes('MUSIC_U=') ? '✅ 从环境变量加载' : '❌ 环境变量未设置'}`);
  console.log(`🎵 支持功能: 单曲解析、批量下载、状态检测`);
  console.log(`📡 服务地址: http://localhost:${config.PORT}`);
});
