#!/bin/bash

echo "🎵 启动偷听 Node.js 版本"
echo ""

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "❌ 错误：未检测到Node.js，请先安装Node.js"
    echo "下载地址：https://nodejs.org/"
    exit 1
fi

# 检查依赖是否安装
if [ ! -d "node_modules" ]; then
    echo "📦 首次运行，正在安装依赖..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ 依赖安装失败"
        exit 1
    fi
fi

# 检查环境变量文件
if [ ! -f ".env" ]; then
    echo "⚠️ 未找到.env文件，正在创建..."
    cp .env.example .env
    echo ""
    echo "🔧 请编辑.env文件，设置NETEASE_COOKIE环境变量"
    echo "然后重新运行此脚本"
    exit 0
fi

echo "🚀 启动服务器..."
echo "📡 访问地址：http://localhost:3004"
echo "按 Ctrl+C 停止服务器"
echo ""

npm start
