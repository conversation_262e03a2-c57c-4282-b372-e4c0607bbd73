// 单曲解析 API
require('dotenv').config();
const { NeteaseEAPI } = require('../lib/netease-eapi-simple.js');

const api = new NeteaseEAPI();

// 短链接解析
async function resolveShortLink(shortCode) {
  try {
    const shortUrl = `http://163cn.tv/${shortCode}`;
    
    const response = await fetch(shortUrl, {
      method: 'HEAD',
      redirect: 'manual'
    });

    const location = response.headers.get('location');
    if (location) {
      const match = location.match(/id=(\d+)/);
      if (match) {
        return match[1];
      }
    }

    return null;
  } catch (error) {
    console.error('短链接解析失败:', error);
    return null;
  }
}

module.exports = async function handler(req, res) {
  // 设置 CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    let { ids, url: songUrl, level = 'lossless' } = req.body;
    let songId = ids || songUrl;

    console.log('🎵 收到单曲解析请求:', { songId, level });

    if (!songId) {
      return res.status(400).json({
        status: 400,
        error: '必须提供歌曲ID或短链接'
      });
    }

    // 如果是短链接代码，先解析
    if (typeof songId === 'string' && songId.length < 10 && !/^\d+$/.test(songId)) {
      console.log(`🔗 解析短链接: ${songId}`);
      const resolvedId = await resolveShortLink(songId);
      if (resolvedId) {
        songId = resolvedId;
        console.log(`✅ 短链接解析成功: ${songId}`);
      } else {
        return res.status(400).json({
          status: 400,
          error: '短链接解析失败'
        });
      }
    }

    console.log(`🎵 解析歌曲: ${songId}, 音质: ${level}`);

    // 检查Cookie是否有效
    const cookieString = process.env.NETEASE_COOKIE;
    if (!cookieString || !cookieString.includes('MUSIC_U=')) {
      return res.status(400).json({
        status: 400,
        error: '服务器未配置有效的NETEASE_COOKIE环境变量，请联系管理员设置'
      });
    }

    const parsedCookies = api.parseCookie(cookieString);
    const cookies = api.createFullCookieObject(parsedCookies);

    // 测试Cookie有效性
    console.log('🍪 测试Cookie有效性...');
    const cookieValid = await api.testCookie(cookies);
    console.log('🍪 Cookie测试结果:', cookieValid ? '✅ 有效' : '❌ 无效');

    const result = await api.url_v1(songId, level, cookies);

    if (!result?.data?.[0]) {
      return res.status(400).json({
        status: 400,
        error: '无法获取歌曲信息'
      });
    }

    const songData = result.data[0];

    if (!songData.url) {
      return res.status(400).json({
        status: 400,
        error: '无法获取歌曲URL，可能是版权限制或需要会员权限'
      });
    }

    // 获取歌曲详情
    let songInfo = {};
    try {
      songInfo = await api.getSongDetail(songId);
    } catch (error) {
      console.warn('获取歌曲详情失败:', error.message);
    }

    const responseData = {
      status: 200,
      name: songInfo?.name || `歌曲ID: ${songId}`,
      ar_name: songInfo?.ar?.map(artist => artist.name).join('/') || '网易云音乐',
      al_name: songInfo?.al?.name || '专辑信息',
      level: api.getQualityName(level),
      size: api.formatFileSize(songData.size),
      url: songData.url.replace('http://', 'https://'),
      br: songData.br,
      pic: songInfo?.al?.picUrl || '',
      debug: {
        requestedLevel: level,
        actualBr: songData.br,
        isLossless: api.isLosslessQuality(songData.br),
        environment: process.env.NODE_ENV || 'development'
      }
    };

    if (level === 'lossless' && api.isLosslessQuality(songData.br)) {
      responseData.note = `🎉 成功获取无损音质! 比特率: ${songData.br}`;
    } else if (level === 'lossless') {
      responseData.note = `⚠️ 请求无损但获取到较低音质，比特率: ${songData.br || '未知'}`;
    }

    res.json(responseData);

  } catch (error) {
    console.error('单曲解析错误:', error);
    res.status(500).json({
      status: 500,
      error: '服务器内部错误'
    });
  }
}
