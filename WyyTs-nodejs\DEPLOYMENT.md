# 🚀 部署指南

## 本地部署

### Windows 用户

1. **双击运行 `start.bat`**
   - 脚本会自动检查环境并安装依赖
   - 首次运行会创建 `.env` 文件
   - 按提示配置 Cookie 后重新运行

2. **手动运行**
   ```cmd
   npm install
   copy .env.example .env
   # 编辑 .env 文件设置 NETEASE_COOKIE
   npm start
   ```

### Linux/macOS 用户

1. **运行启动脚本**
   ```bash
   chmod +x start.sh
   ./start.sh
   ```

2. **手动运行**
   ```bash
   npm install
   cp .env.example .env
   # 编辑 .env 文件设置 NETEASE_COOKIE
   npm start
   ```

## 服务器部署

### 使用 PM2 (推荐)

1. **安装 PM2**
   ```bash
   npm install -g pm2
   ```

2. **创建 PM2 配置文件**
   ```javascript
   // ecosystem.config.js
   module.exports = {
     apps: [{
       name: 'wyyts-nodejs',
       script: 'server.js',
       instances: 1,
       autorestart: true,
       watch: false,
       max_memory_restart: '1G',
       env: {
         NODE_ENV: 'production',
         PORT: 3004
       }
     }]
   };
   ```

3. **启动服务**
   ```bash
   pm2 start ecosystem.config.js
   pm2 save
   pm2 startup
   ```

### 使用 Docker

1. **创建 Dockerfile**
   ```dockerfile
   FROM node:18-alpine
   
   WORKDIR /app
   
   COPY package*.json ./
   RUN npm ci --only=production
   
   COPY . .
   
   EXPOSE 3004
   
   USER node
   
   CMD ["npm", "start"]
   ```

2. **构建和运行**
   ```bash
   docker build -t wyyts-nodejs .
   docker run -d -p 3004:3004 --env-file .env wyyts-nodejs
   ```

### 使用 Nginx 反向代理

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:3004;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## 环境变量配置

### 必需配置

- `NETEASE_COOKIE`: 网易云音乐Cookie（必需）
- `PORT`: 服务端口（默认3004）

### 可选配置

- `NODE_ENV`: 运行环境（development/production）
- `DEBUG`: 调试模式（true/false）
- `SEARCH_LIMIT`: 搜索结果限制（默认50）
- `DOWNLOAD_CONCURRENCY`: 下载并发数（默认3）

### Cookie 获取方法

1. 登录网易云音乐网页版
2. 打开开发者工具 (F12)
3. 在 Network 标签页找到任意请求
4. 复制 Cookie 头部的值
5. 设置到环境变量 `NETEASE_COOKIE`

## 性能优化

### 生产环境建议

1. **设置环境变量**
   ```bash
   export NODE_ENV=production
   ```

2. **启用 Gzip 压缩**
   - 在 Nginx 中启用 gzip
   - 或使用 Express 中间件

3. **设置缓存策略**
   - 静态文件缓存
   - API 响应缓存

4. **监控和日志**
   - 使用 PM2 监控
   - 配置日志轮转

## 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查找占用端口的进程
   netstat -ano | findstr :3004  # Windows
   lsof -i :3004                 # Linux/macOS
   ```

2. **Cookie 失效**
   - 重新获取 Cookie
   - 检查 Cookie 格式

3. **依赖安装失败**
   ```bash
   # 清除缓存重新安装
   npm cache clean --force
   rm -rf node_modules package-lock.json
   npm install
   ```

4. **内存不足**
   - 增加服务器内存
   - 设置 PM2 内存限制

### 日志查看

```bash
# PM2 日志
pm2 logs wyyts-nodejs

# 实时日志
pm2 logs wyyts-nodejs --lines 100 -f
```

## 安全建议

1. **不要暴露敏感信息**
   - Cookie 不要提交到代码仓库
   - 使用环境变量管理敏感配置

2. **设置防火墙**
   - 只开放必要端口
   - 使用 HTTPS

3. **定期更新**
   - 定期更新依赖包
   - 关注安全漏洞

## 备份和恢复

### 备份内容

- 配置文件 (`.env`)
- 自定义修改的代码
- 用户数据（如果有）

### 恢复步骤

1. 重新部署代码
2. 恢复配置文件
3. 安装依赖
4. 启动服务

---

如有问题，请查看项目 README.md 或提交 Issue。
