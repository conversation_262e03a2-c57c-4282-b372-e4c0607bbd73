// 测试 API - 验证部署是否成功
module.exports = function handler(req, res) {
  // 设置 CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  res.json({
    status: 200,
    message: '🎉 Vercel Serverless Functions 部署成功！',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    hasNeteaseCookie: !!(process.env.NETEASE_COOKIE && process.env.NETEASE_COOKIE.includes('MUSIC_U=')),
    method: req.method,
    url: req.url
  });
}
