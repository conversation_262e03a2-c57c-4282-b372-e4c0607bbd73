/**
 * 主逻辑模块 - 连接各个模块，处理主要业务逻辑
 */

/**
 * 切换选项卡
 */
function showTab(tabName) {
    window.uiController.showTab(tabName);
}

/**
 * 自动解析输入内容
 */
function autoParseInput(type) {
    window.uiController.autoParseInput(type);
}

/**
 * 解析单曲
 */
async function parseSingle() {
    const input = document.getElementById('singleInput').value.trim();
    const quality = document.getElementById('singleQuality').value;

    if (!input) {
        window.uiController.showError('请输入单曲分享内容或链接');
        return;
    }

    // 解析输入内容
    const parseResult = window.musicParser.parseInput(input);
    if (!parseResult || parseResult.type !== 'song') {
        window.uiController.showError('无法识别单曲信息，请检查输入内容');
        return;
    }

    const formatted = window.musicParser.formatResult(parseResult);
    if (!formatted) {
        window.uiController.showError('解析失败，请检查输入格式');
        return;
    }

    window.uiController.showLoading('正在解析单曲...');

    try {
        // 直接传递ID给后端，让后端处理短链接解析
        const result = await window.musicAPI.parseSong(formatted.id, quality);

        if (result.success) {
            const data = result.data;
            const extractedInfo = formatted.extractedInfo;

            // 显示解析结果
            window.uiController.showSuccess(`
                <div style="position: relative;">
                    <h3>✅ 解析成功</h3>
                    <div style="position: absolute; top: 0; right: 0; display: flex; gap: 8px;">
                        <button id="playBtn_${formatted.id}"
                                data-url="${data.url}"
                                data-title="${(extractedInfo.songName || data.name).replace(/"/g, '&quot;')}"
                                data-artist="${(extractedInfo.artist || data.ar_name).replace(/"/g, '&quot;')}"
                                data-quality="${data.level}"
                                onclick="playSongFromButton(this)"
                                style="padding: 8px 16px; font-size: 16px; border-radius: 10px; background: linear-gradient(135deg, rgba(255, 107, 157, 0.8), rgba(196, 113, 237, 0.8)); color: white; border: none; cursor: pointer; box-shadow: 0 4px 12px rgba(255, 107, 157, 0.3); transition: all 0.3s ease; font-weight: 600;">
                            试听
                        </button>
                        <button onclick="downloadSingleSong('${formatted.id}', '${quality}')"
                                style="padding: 8px 16px; font-size: 16px; border-radius: 10px; background: linear-gradient(135deg, rgba(18, 194, 233, 0.8), rgba(196, 113, 237, 0.8)); color: white; border: none; cursor: pointer; box-shadow: 0 4px 12px rgba(18, 194, 233, 0.3); transition: all 0.3s ease; font-weight: 600;">
                            下载
                        </button>
                    </div>
                    <p><strong>歌曲：</strong>${extractedInfo.songName || data.name}</p>
                    <p><strong>艺术家：</strong>${extractedInfo.artist || data.ar_name}</p>
                    <p><strong>专辑：</strong>${data.al_name}</p>
                    <p><strong>音质：</strong>${data.level}</p>
                    <p><strong>大小：</strong>${data.size}</p>
                    ${data.note ? `<p><strong>说明：</strong>${data.note}</p>` : ''}
                </div>
            `);
        } else {
            window.uiController.showError(result.error);
        }
    } catch (error) {
        console.error('解析单曲失败:', error);
        window.uiController.showError(error.message);
    }
}

/**
 * 下载单曲
 */
async function downloadSingleSong(songId, quality) {
    window.uiController.showLoading('正在获取下载链接...');

    try {
        const result = await window.musicAPI.parseSong(songId, quality);

        if (result.success && result.data.url) {
            const data = result.data;
            const extension = (quality === 'lossless' || quality === 'hires') ? 'flac' : 'mp3';
            const filename = `${data.ar_name} - ${data.name}`;

            // 下载文件
            await window.musicAPI.downloadFile(result.data.url, filename, quality);

            window.uiController.showSuccess(`
                <h3>✅ 下载开始</h3>
                <p><strong>文件名：</strong>${filename}.${extension}</p>
                <p><strong>音质：</strong>${data.level}</p>
                <p><strong>大小：</strong>${data.size}</p>
            `);
        } else {
            window.uiController.showError('无法获取下载链接');
        }
    } catch (error) {
        console.error('下载失败:', error);
        window.uiController.showError(error.message);
    }
}

/**
 * 加载歌单/专辑列表 - 完全兼容普通歌单和"我喜欢的歌单"token格式
 */
async function loadPlaylist() {
    const type = document.getElementById('batchType').value;
    const input = document.getElementById('batchInput').value.trim();

    if (!input) {
        window.uiController.showError('请输入歌单或专辑分享内容');
        return;
    }

    console.log('🎵 开始加载歌单/专辑:', { type, input: input.substring(0, 100) + '...' });
    window.uiController.showLoading(`正在加载${type === 'playlist' ? '歌单' : '专辑'}信息...`);

    try {
        let requestBody;
        let detectedType = type;

        // 🔑 优先检查是否是落雪音乐的token注入格式 (URL###TOKEN)
        if (input.includes('###')) {
            console.log('🔑 检测到落雪音乐token格式 (URL###TOKEN)');

            // 解析URL和token
            const [urlPart, tokenPart] = input.split('###');
            const cleanUrl = urlPart.trim();
            const userToken = tokenPart.trim();

            console.log('📋 分离结果:', {
                urlPart: cleanUrl,
                tokenLength: userToken.length,
                tokenPreview: userToken.substring(0, 50) + '...'
            });

            // 从URL中提取歌单ID
            const playlistId = extractPlaylistId(cleanUrl);

            if (!playlistId) {
                throw new Error(`无法从URL中解析歌单ID: ${cleanUrl}`);
            }

            if (!userToken || userToken.length < 100) {
                throw new Error(`Token格式不正确，长度: ${userToken.length}，期望 > 100`);
            }

            // 构建包含token的请求体
            requestBody = {
                type: 'playlist',
                id: playlistId,
                token: userToken
            };

            detectedType = 'playlist';
            console.log('🔑 构建私人歌单请求:', { id: playlistId, tokenLength: userToken.length });

        } else {
            // 📋 普通歌单/专辑格式处理
            console.log('📋 处理普通歌单/专辑格式');

            // 尝试智能解析
            const parseResult = window.musicParser.parseInput(input);
            let id = input;

            if (parseResult) {
                const formatted = window.musicParser.formatResult(parseResult);
                if (formatted && (formatted.type === 'playlist' || formatted.type === 'album')) {
                    id = formatted.id;
                    detectedType = formatted.type;

                    // 自动更新类型选择
                    document.getElementById('batchType').value = detectedType;
                    console.log('📋 智能解析成功:', { type: detectedType, id });
                }
            } else {
                // 如果智能解析失败，尝试直接提取ID
                const directId = extractPlaylistId(input);
                if (directId) {
                    id = directId;
                    console.log('📋 直接提取ID成功:', id);
                }
            }

            requestBody = {
                type: detectedType,
                id: id
            };

            console.log('📋 构建公开歌单/专辑请求:', requestBody);
        }

        console.log('📡 发送API请求:', requestBody);

        // 直接调用API
        const response = await fetch('/api/playlist-info', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
        });

        console.log('📡 API响应状态:', response.status);
        const data = await response.json();
        console.log('📡 API响应数据:', data);

        if (data.status === 200) {
            console.log('✅ 加载成功:', {
                name: data.name,
                songCount: data.songs?.length || 0,
                type: detectedType
            });
            window.uiController.showPlaylistSongs(data.songs, data.name);
        } else {
            console.error('❌ API返回错误:', data.error);
            throw new Error(data.error);
        }
    } catch (error) {
        console.error('❌ 加载失败:', error);
        window.uiController.showError(`加载失败: ${error.message}`);
    }
}

/**
 * 切换全选状态
 */
function toggleSelectAll() {
    window.uiController.toggleSelectAll();
}

/**
 * 下载选中的歌曲 - 限制每次最多30首
 */
async function downloadSelected() {
    const selectedSongs = window.uiController.getSelectedSongs();

    if (selectedSongs.length === 0) {
        window.uiController.showError('请选择要下载的歌曲');
        return;
    }

    // 检查选中歌曲数量限制
    if (selectedSongs.length > 30) {
        window.uiController.showError(`每次批量下载最多支持30首歌曲，您选择了${selectedSongs.length}首。请减少选择数量。`);
        return;
    }

    const quality = document.getElementById('batchQuality').value;

    try {
        console.log(`🎵 开始批量下载 ${selectedSongs.length} 首歌曲`);
        const results = await window.musicAPI.batchDownload(selectedSongs, quality);
        window.uiController.showDownloadComplete(results);
    } catch (error) {
        console.error('❌ 批量下载失败:', error);
        window.uiController.showError(error.message);
    }
}

/**
 * 从URL中提取歌单ID
 */
function extractPlaylistId(url) {
    // 支持多种URL格式
    const patterns = [
        /playlist\?id=(\d+)/,           // 标准格式
        /m\/playlist\?id=(\d+)/,        // 移动端格式
        /playlist\/(\d+)/               // 简化格式
    ];

    for (const pattern of patterns) {
        const match = url.match(pattern);
        if (match) {
            return match[1];
        }
    }

    // 如果是纯数字，直接作为歌单ID
    if (/^\d+$/.test(url)) {
        return url;
    }

    return null;
}

/**
 * 页面初始化
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('🎵 偷听已加载');

    // 初始化默认选项卡
    showTab('single');

    // 检查所有模块是否正确加载
    if (!window.musicAPI) {
        console.error('❌ API模块加载失败');
    }
    if (!window.musicParser) {
        console.error('❌ 解析模块加载失败');
    }
    if (!window.uiController) {
        console.error('❌ UI控制模块加载失败');
    }

    console.log('✅ 所有模块加载完成');
});
