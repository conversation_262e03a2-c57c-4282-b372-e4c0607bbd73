// 服务状态 API
require('dotenv').config();

module.exports = function handler(req, res) {
  // 设置 CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const hasValidCookie = process.env.NETEASE_COOKIE && process.env.NETEASE_COOKIE.includes('MUSIC_U=');

  res.json({
    status: 200,
    service: '网易云音乐解析器',
    version: 'Node.js版 (Vercel Serverless)',
    environment: process.env.NODE_ENV || 'development',
    cookieConfigured: hasValidCookie,
    message: hasValidCookie ? '服务正常运行' : '需要配置NETEASE_COOKIE环境变量',
    configHelp: {
      environment: '环境变量设置',
      vercelEnvironment: '在 Vercel Dashboard 设置环境变量',
      cookieFormat: 'MUSIC_U=your_music_u_value;os=pc;appver=8.9.75;'
    }
  });
}
