// 歌单/专辑信息 API
import dotenv from 'dotenv';
import { NeteaseEAPI } from '../lib/netease-eapi.js';

// 加载环境变量
dotenv.config();

const api = new NeteaseEAPI();

export default async function handler(req, res) {
  // 设置 CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    let { type, id, url, token } = req.body;

    console.log('📋 收到请求体:', req.body);

    // 支持落雪音乐的URL###TOKEN格式（兼容处理）
    if (url && url.includes('###')) {
      const [urlPart, tokenPart] = url.split('###');
      url = urlPart.trim();
      token = tokenPart.trim();
      console.log('🔑 检测到落雪音乐URL###TOKEN格式，token长度:', token.length);

      // 从URL中提取ID
      const patterns = [
        /playlist\?id=(\d+)/,           // 标准格式
        /m\/playlist\?id=(\d+)/,        // 移动端格式
        /playlist\/(\d+)/               // 简化格式
      ];

      for (const pattern of patterns) {
        const match = url.match(pattern);
        if (match) {
          id = match[1];
          type = 'playlist';
          break;
        }
      }

      // 如果是纯数字，直接作为歌单ID
      if (!id && /^\d+$/.test(url)) {
        id = url;
        type = 'playlist';
      }
    }

    // 如果前端直接传递了token参数（新的处理方式）
    if (token) {
      console.log('🔑 检测到前端传递的token参数，token长度:', token.length);
    }

    console.log(`📋 获取歌单信息: 类型=${type}, ID=${id}, 有token=${!!token}`);

    // 构建cookies - 区分普通歌单和私人歌单
    let cookies;

    if (token) {
      // 使用用户提供的token (适用于"我喜欢的歌单"等私人歌单)
      cookies = {
        MUSIC_U: token,
        os: 'pc',
        appver: '8.9.75'
      };
      console.log('🍪 使用用户提供的token (私人歌单)');
    } else {
      // 普通歌单：优先使用环境变量Cookie，如果没有则尝试无Cookie访问
      const cookieString = process.env.NETEASE_COOKIE;
      if (cookieString && cookieString.includes('MUSIC_U=')) {
        const parsedCookies = api.parseCookie(cookieString);
        cookies = api.createFullCookieObject(parsedCookies);
        console.log('🍪 使用环境变量cookie (普通歌单)');
      } else {
        // 对于公开歌单，尝试无Cookie访问
        cookies = {
          os: 'pc',
          appver: '8.9.75'
        };
        console.log('🍪 使用基础cookie (公开歌单)');
      }
    }

    let result;

    if (type === 'album') {
      result = await api.getAlbumInfo(id, cookies);
    } else if (type === 'playlist') {
      result = await api.getPlaylistInfo(id, cookies);
    } else {
      throw new Error('无效的类型参数');
    }

    res.json({
      status: 200,
      ...result
    });

  } catch (error) {
    console.error('获取歌单信息错误:', error);
    res.status(500).json({
      status: 500,
      error: `获取歌单信息失败: ${error.message}`
    });
  }
}
