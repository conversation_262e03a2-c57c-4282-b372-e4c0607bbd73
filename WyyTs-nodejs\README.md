# 🎵 偷听 - Node.js版本

网易云音乐解析器的Node.js版本，从Deno版本完整迁移而来，保持所有前端界面风格和功能不变。

## ✨ 功能特性

- 🎵 **单曲解析** - 支持网易云音乐单曲链接和短链接解析
- 📋 **批量下载** - 支持歌单和专辑批量下载
- 🎧 **多音质支持** - 无损音质(FLAC)、Hi-Res音质、母带音质
- 🔗 **智能解析** - 自动识别分享链接格式
- 🎨 **精美界面** - 保持原版孤独美学设计风格
- 🔐 **Cookie支持** - 支持会员账户和私人歌单访问

## 🚀 快速开始

### 环境要求

- Node.js >= 16.0.0
- npm 或 yarn

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd WyyTs-nodejs
   ```

2. **安装依赖**
   ```bash
   npm install
   # 或
   yarn install
   ```

3. **配置环境变量**
   ```bash
   cp .env.example .env
   ```
   
   编辑 `.env` 文件，设置必要的环境变量：
   ```env
   PORT=3004
   NODE_ENV=development
   NETEASE_COOKIE=your_cookie_here
   ```

4. **启动服务**
   ```bash
   # 开发模式
   npm run dev
   
   # 生产模式
   npm start
   ```

5. **访问应用**
   
   打开浏览器访问：http://localhost:3004

## 🍪 Cookie配置

为了获取无损音质和访问私人歌单，需要配置网易云音乐Cookie：

### 获取Cookie步骤：

1. 登录网易云音乐网页版 (music.163.com)
2. 打开浏览器开发者工具 (F12)
3. 在Network标签页中找到任意请求
4. 复制Cookie头部的值
5. 将Cookie值设置到环境变量 `NETEASE_COOKIE`

### Cookie格式示例：
```
MUSIC_U=your_music_u_value;os=pc;appver=8.9.75;
```

## 📁 项目结构

```
WyyTs-nodejs/
├── server.js              # 主服务器文件
├── lib/
│   └── netease-eapi.js    # 网易云EAPI实现
├── public/                # 前端静态文件
│   ├── index.html         # 主页面
│   ├── css/
│   │   └── style.css      # 样式文件
│   └── js/
│       ├── api.js         # API通信模块
│       ├── parser.js      # 链接解析模块
│       ├── ui.js          # UI控制模块
│       └── main.js        # 主逻辑模块
├── package.json           # 项目配置
├── .env.example          # 环境变量示例
└── README.md             # 项目说明
```

## 🔧 API接口

### 单曲解析
```
POST /api/song
Content-Type: application/json

{
  "ids": "歌曲ID或短链接",
  "level": "lossless"
}
```

### 歌单信息
```
POST /api/playlist-info
Content-Type: application/json

{
  "type": "playlist",
  "id": "歌单ID",
  "token": "用户token(可选)"
}
```

### Cookie检查
```
POST /api/cookie-check
Content-Type: application/json

{}
```

## 🎨 界面特色

- **孤独美学设计** - 深色主题配合渐变背景
- **文学引用轮播** - 精选文学名句营造氛围
- **响应式布局** - 支持桌面端和移动端
- **动画效果** - 流畅的交互动画
- **音乐播放器** - 内置试听功能

## 🔄 从Deno版本迁移

本项目完整保留了Deno版本的所有功能：

- ✅ 前端界面完全一致
- ✅ 所有API功能保持不变
- ✅ 支持相同的链接格式
- ✅ 保持相同的音质选项
- ✅ 兼容落雪音乐token格式

## 📝 使用说明

### 单曲解析
1. 在"单曲解析"标签页输入网易云音乐分享内容
2. 选择音质等级
3. 点击"解析单曲"按钮
4. 可以试听或直接下载

### 批量下载
1. 在"批量下载"标签页选择类型（歌单/专辑）
2. 输入分享内容或ID
3. 点击"加载歌曲列表"
4. 选择要下载的歌曲（最多30首）
5. 点击"下载选中"开始批量下载

## ⚠️ 注意事项

- 本项目仅供学习交流使用
- 请尊重音乐版权，支持正版音乐
- 下载的音乐文件请勿用于商业用途
- 建议使用会员账户获得更好的音质体验

## 🛠️ 开发

### 开发模式
```bash
npm run dev
```

### 生产构建
```bash
npm start
```

### 依赖说明
- `express` - Web服务器框架
- `cors` - 跨域支持
- `node-fetch` - HTTP请求库
- `archiver` - ZIP文件生成
- `fs-extra` - 文件系统扩展

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request！

---

**Claude 4.0 sonnet** 倾情打造 🎵
