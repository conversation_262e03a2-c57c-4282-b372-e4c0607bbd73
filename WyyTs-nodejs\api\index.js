// 主页面路由 - 服务静态文件
const { readFileSync } = require('fs');
const { join } = require('path');

module.exports = function handler(req, res) {
  try {
    // 读取 index.html 文件
    const htmlPath = join(__dirname, '..', 'public', 'index.html');
    const html = readFileSync(htmlPath, 'utf8');

    res.setHeader('Content-Type', 'text/html');
    res.status(200).send(html);
  } catch (error) {
    console.error('Error serving index.html:', error);
    console.error('Attempted path:', join(__dirname, '..', 'public', 'index.html'));
    res.status(500).json({ error: 'Internal Server Error', details: error.message });
  }
}
