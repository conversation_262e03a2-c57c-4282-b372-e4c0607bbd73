// 主页面 API - 返回 HTML 内容
const fs = require('fs');
const path = require('path');

module.exports = function handler(req, res) {
  try {
    // 读取 public/index.html 文件
    const htmlPath = path.join(process.cwd(), 'public', 'index.html');
    const html = fs.readFileSync(htmlPath, 'utf8');
    
    res.setHeader('Content-Type', 'text/html; charset=utf-8');
    res.status(200).send(html);
  } catch (error) {
    console.error('读取 index.html 失败:', error);
    
    // 如果读取失败，返回一个简单的 HTML
    const fallbackHtml = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎵偷听</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            padding: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            margin: 0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        h1 { font-size: 2.5em; margin-bottom: 20px; }
        p { font-size: 1.2em; margin: 15px 0; }
        .status { color: #90EE90; }
        .error { color: #FFB6C1; }
        a { color: #87CEEB; text-decoration: none; }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎵 偷听</h1>
        <p class="status">✅ 服务器运行正常</p>
        <p>网易云音乐解析器 - Node.js 版本</p>
        <p class="error">⚠️ 静态文件加载失败，使用备用页面</p>
        
        <h3>🧪 API 测试</h3>
        <p><a href="/api/test">测试接口</a></p>
        <p><a href="/api/status">服务状态</a></p>
        
        <h3>📋 功能接口</h3>
        <p>POST /api/song - 单曲解析</p>
        <p>POST /api/batch-download - 批量下载</p>
        <p>POST /api/playlist-info - 歌单信息</p>
        <p>POST /api/cookie-check - Cookie检查</p>
        
        <p style="margin-top: 30px; font-size: 0.9em; opacity: 0.8;">
            错误详情: ${error.message}
        </p>
    </div>
</body>
</html>`;
    
    res.setHeader('Content-Type', 'text/html; charset=utf-8');
    res.status(200).send(fallbackHtml);
  }
};
