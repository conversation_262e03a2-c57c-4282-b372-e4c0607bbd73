@echo off
echo 🎵 启动偷听 Node.js 版本
echo.

REM 检查Node.js是否安装
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误：未检测到Node.js，请先安装Node.js
    echo 下载地址：https://nodejs.org/
    pause
    exit /b 1
)

REM 检查依赖是否安装
if not exist "node_modules" (
    echo 📦 首次运行，正在安装依赖...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
)

REM 检查环境变量文件
if not exist ".env" (
    echo ⚠️ 未找到.env文件，正在创建...
    copy .env.example .env
    echo.
    echo 🔧 请编辑.env文件，设置NETEASE_COOKIE环境变量
    echo 然后重新运行此脚本
    pause
    exit /b 0
)

echo 🚀 启动服务器...
echo 📡 访问地址：http://localhost:3004
echo 按 Ctrl+C 停止服务器
echo.

npm start
