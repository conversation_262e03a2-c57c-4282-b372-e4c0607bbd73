/**
 * 简化版网易云EAPI实现 - CommonJS版本
 * 专为 Vercel Serverless Functions 优化
 */

const crypto = require('crypto');
const fetch = require('node-fetch');

// MD5实现
class MD5 {
  static hash(str) {
    return crypto.createHash('md5').update(str, 'utf8').digest('hex');
  }
}

// 网易云EAPI类
class NeteaseEAPI {
  constructor() {
    this.AES_KEY = Buffer.from('e82ckenh8dichen8', 'utf8');
    this.BASE_URL = 'https://interface3.music.163.com';
  }

  /**
   * 十六进制摘要
   */
  hexDigest(data) {
    return Array.from(data)
      .map(byte => byte.toString(16).padStart(2, '0'))
      .join('');
  }

  /**
   * MD5哈希摘要
   */
  hashDigest(text) {
    const hexString = MD5.hash(text);
    const bytes = Buffer.alloc(16);
    for (let i = 0; i < 16; i++) {
      bytes[i] = parseInt(hexString.substr(i * 2, 2), 16);
    }
    return bytes;
  }

  /**
   * MD5哈希十六进制摘要
   */
  hashHexDigest(text) {
    return MD5.hash(text);
  }

  /**
   * AES ECB加密
   */
  aesEncrypt(plaintext) {
    console.log('🔐 AES-ECB加密调试:');
    console.log('   原文长度:', plaintext.length);
    console.log('   密钥:', this.AES_KEY.toString('hex'));

    const cipher = crypto.createCipheriv('aes-128-ecb', this.AES_KEY, null);
    cipher.setAutoPadding(true);
    
    let encrypted = cipher.update(plaintext, 'utf8');
    encrypted = Buffer.concat([encrypted, cipher.final()]);

    console.log('   ECB加密结果长度:', encrypted.length);
    console.log('   ECB加密结果前32字节:', encrypted.slice(0, 32).toString('hex'));

    return encrypted;
  }

  /**
   * 网易云EAPI请求
   */
  async url_v1(songId, level, cookies) {
    const url = `${this.BASE_URL}/eapi/song/enhance/player/url/v1`;
    
    const config = {
      "os": "pc",
      "appver": "",
      "osver": "",
      "deviceId": "pyncm!",
      "requestId": Math.floor(Math.random() * (30000000 - 20000000 + 1) + 20000000).toString()
    };

    const payload = {
      'ids': [songId],
      'level': level,
      'encodeType': 'flac',
      'header': JSON.stringify(config)
    };

    if (level === 'sky') {
      payload['immerseType'] = 'c51';
    }

    const urlPath = '/api/song/enhance/player/url/v1';
    const payloadStr = JSON.stringify(payload);
    const digestText = `nobody${urlPath}use${payloadStr}md5forencrypt`;
    const digest = this.hashHexDigest(digestText);
    const paramsText = `${urlPath}-36cd479b6b5-${payloadStr}-36cd479b6b5-${digest}`;

    const encrypted = this.aesEncrypt(paramsText);
    const encryptedHex = this.hexDigest(encrypted);

    const cookieString = Object.entries(cookies)
      .map(([key, value]) => `${key}=${value}`)
      .join('; ');

    console.log('🔧 EAPI调试信息:');
    console.log('   URL:', url);
    console.log('   Payload:', payloadStr);
    console.log('   DigestText:', digestText);
    console.log('   Digest:', digest);
    console.log('   ParamsText:', paramsText);
    console.log('   EncryptedHex长度:', encryptedHex.length);
    console.log('   Cookie长度:', cookieString.length);

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.164 Safari/537.36',
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Content-Type': 'application/x-www-form-urlencoded',
        'Origin': 'https://music.163.com',
        'Referer': 'https://music.163.com/',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-site',
        'Cookie': cookieString
      },
      body: `params=${encryptedHex}`
    });

    console.log('📡 EAPI响应状态:', response.status);

    if (!response.ok) {
      throw new Error(`EAPI请求失败: ${response.status}`);
    }

    const responseText = await response.text();
    console.log('📡 EAPI响应内容长度:', responseText.length);
    console.log('📡 EAPI响应内容前200字符:', responseText.substring(0, 200));

    if (!responseText || responseText.trim() === '') {
      throw new Error('EAPI返回空响应');
    }

    try {
      const result = JSON.parse(responseText);
      console.log('📡 EAPI解析结果:', JSON.stringify(result, null, 2));
      return result;
    } catch (error) {
      console.error('📡 JSON解析失败，原始响应:', responseText);
      throw new Error(`JSON解析失败: ${error.message}`);
    }
  }

  /**
   * 测试Cookie有效性
   */
  async testCookie(cookies) {
    const url = 'https://music.163.com/api/nuser/account/get';
    const cookieString = Object.entries(cookies)
      .map(([key, value]) => `${key}=${value}`)
      .join('; ');

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.164 Safari/537.36',
          'Referer': 'https://music.163.com/',
          'Content-Type': 'application/x-www-form-urlencoded',
          'Cookie': cookieString
        },
        body: ''
      });

      console.log('🍪 Cookie测试响应状态:', response.status);
      const text = await response.text();
      console.log('🍪 Cookie测试响应:', text.substring(0, 200));

      return response.ok && text.includes('account');
    } catch (error) {
      console.log('🍪 Cookie测试失败:', error.message);
      return false;
    }
  }

  /**
   * 获取歌曲详细信息
   */
  async getSongDetail(songId) {
    const url = 'https://interface3.music.163.com/api/v3/song/detail';
    const data = new URLSearchParams();
    data.append('c', JSON.stringify([{"id": parseInt(songId), "v": 0}]));

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Safari/537.36 Chrome/91.0.4472.164 NeteaseMusicDesktop/2.10.2.200154',
        'Referer': 'https://music.163.com/',
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: data
    });

    if (response.ok) {
      const result = await response.json();
      return result.songs?.[0] || null;
    }

    return null;
  }

  /**
   * 解析Cookie字符串
   */
  parseCookie(cookieString) {
    const cookies = {};
    cookieString.split(';').forEach(cookie => {
      const [key, value] = cookie.trim().split('=');
      if (key && value) {
        cookies[key] = value;
      }
    });
    return cookies;
  }

  /**
   * 创建完整的Cookie对象
   */
  createFullCookieObject(parsedCookies) {
    return {
      "os": "pc",
      "appver": "",
      "osver": "",
      "deviceId": "pyncm!",
      ...parsedCookies
    };
  }

  /**
   * 获取音质等级的中文名称
   */
  getQualityName(level) {
    const qualityNames = {
      'standard': '标准音质',
      'exhigh': '极高音质', 
      'lossless': '无损音质',
      'hires': 'Hi-Res音质',
      'sky': '沉浸环绕声',
      'jyeffect': '高清环绕声',
      'jymaster': '超清母带'
    };
    return qualityNames[level] || '未知音质';
  }

  /**
   * 格式化文件大小
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '未知大小';
    return `${(bytes / 1024 / 1024).toFixed(2)}MB`;
  }

  /**
   * 检查是否为无损音质
   */
  isLosslessQuality(br) {
    return br >= 900000;
  }

  /**
   * 获取用户信息
   */
  async getUserInfo(cookies) {
    const url = 'https://music.163.com/api/nuser/account/get';
    const cookieString = Object.entries(cookies)
      .map(([key, value]) => `${key}=${value}`)
      .join('; ');

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.164 Safari/537.36',
          'Referer': 'https://music.163.com/',
          'Content-Type': 'application/x-www-form-urlencoded',
          'Cookie': cookieString
        },
        body: ''
      });

      if (response.ok) {
        const result = await response.json();
        return result;
      }
      return null;
    } catch (error) {
      console.error('获取用户信息失败:', error);
      return null;
    }
  }

  /**
   * 获取歌单信息（简化版）
   */
  async getPlaylistInfo(playlistId, cookies) {
    try {
      const url = 'https://music.163.com/api/v6/playlist/detail';
      const params = new URLSearchParams({
        id: playlistId,
        n: '1000',
        s: '8'
      });

      const cookieString = cookies ?
        Object.entries(cookies).map(([key, value]) => `${key}=${value}`).join('; ') :
        '';

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Safari/537.36 Chrome/91.0.4472.164 NeteaseMusicDesktop/2.10.2.200154',
          'Referer': 'https://music.163.com/',
          'Content-Type': 'application/x-www-form-urlencoded',
          'Cookie': cookieString
        },
        body: params.toString()
      });

      if (response.ok) {
        const result = await response.json();
        if (result.code === 200 && result.playlist) {
          const playlist = result.playlist;
          return {
            name: playlist.name,
            songs: playlist.tracks.map(track => ({
              id: track.id,
              name: track.name,
              artist: track.ar.map(a => a.name).join('/')
            }))
          };
        }
      }

      throw new Error('获取歌单信息失败');
    } catch (error) {
      console.error('获取歌单信息错误:', error);
      throw error;
    }
  }

  /**
   * 获取专辑信息（简化版）
   */
  async getAlbumInfo(albumId, cookies) {
    try {
      const url = `https://music.163.com/api/v1/album/${albumId}`;

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Safari/537.36 Chrome/91.0.4472.164 NeteaseMusicDesktop/2.10.2.200154',
          'Referer': 'https://music.163.com/',
          'Accept': 'application/json, text/plain, */*',
          'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
        }
      });

      if (response.ok) {
        const result = await response.json();
        if (result.songs && result.songs.length > 0) {
          return {
            name: result.album?.name || '未知专辑',
            songs: result.songs.map(song => ({
              id: song.id,
              name: song.name,
              artist: (song.artists || song.ar || []).map(a => a.name).join('/') || '未知艺术家'
            }))
          };
        }
      }

      throw new Error('获取专辑信息失败');
    } catch (error) {
      console.error('获取专辑信息错误:', error);
      throw error;
    }
  }
}

module.exports = { NeteaseEAPI };
