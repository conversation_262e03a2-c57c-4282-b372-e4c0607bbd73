# 🚀 Vercel 部署指南

## 📋 部署前准备

### 1. 安装 Vercel CLI
```bash
npm install -g vercel
```

### 2. 登录 Vercel
```bash
vercel login
```

## ⚠️ 重要：Vercel 配置选择

项目提供了两种 Vercel 配置文件：

### 选项1：使用传统配置 (推荐)
使用 `vercel.json` - 兼容性更好
```json
{
  "version": 2,
  "builds": [{"src": "server.js", "use": "@vercel/node"}],
  "routes": [{"src": "/(.*)", "dest": "/server.js"}]
}
```

### 选项2：使用现代配置
将 `vercel-modern.json` 重命名为 `vercel.json` - 支持更多功能
```json
{
  "version": 2,
  "functions": {"server.js": {"maxDuration": 30}},
  "rewrites": [{"source": "/(.*)", "destination": "/server.js"}]
}
```

## 🌐 方法一：通过 Vercel CLI 部署

### 1. 在项目根目录运行
```bash
vercel
```

### 2. 按提示配置项目
- 选择账户/团队
- 确认项目名称
- 确认项目设置

### 3. 设置环境变量
```bash
# 设置网易云音乐Cookie（必需）
vercel env add NETEASE_COOKIE

# 设置其他环境变量
vercel env add NODE_ENV production
vercel env add PORT 3000
vercel env add DEBUG false
```

### 4. 重新部署以应用环境变量
```bash
vercel --prod
```

## 🌐 方法二：通过 Vercel Dashboard 部署

### 1. 连接 Git 仓库
1. 访问 [Vercel Dashboard](https://vercel.com/dashboard)
2. 点击 "New Project"
3. 导入你的 Git 仓库（GitHub/GitLab/Bitbucket）

### 2. 配置项目设置
- **Framework Preset**: Other
- **Root Directory**: `./` (如果项目在根目录)
- **Build Command**: `npm run build` (可选)
- **Output Directory**: `public` (静态文件目录)
- **Install Command**: `npm install`

### 3. 设置环境变量
在项目设置页面的 "Environment Variables" 部分添加：

| 变量名 | 值 | 环境 |
|--------|----|----|
| `NETEASE_COOKIE` | `MUSIC_U=你的cookie值;os=pc;appver=8.9.75;` | Production, Preview, Development |
| `NODE_ENV` | `production` | Production |
| `DEBUG` | `false` | Production |
| `SEARCH_LIMIT` | `50` | All |
| `DOWNLOAD_CONCURRENCY` | `3` | All |

### 4. 部署
点击 "Deploy" 按钮开始部署。

## 🔧 环境变量详细设置

### 必需环境变量

#### NETEASE_COOKIE
```
变量名: NETEASE_COOKIE
值: MUSIC_U=你的完整cookie值;os=pc;appver=8.9.75;
环境: Production, Preview, Development
```

**获取方法：**
1. 登录网易云音乐网页版 (music.163.com)
2. 打开开发者工具 (F12)
3. 在 Network 标签页找到任意请求
4. 复制 Cookie 头部的完整值
5. 确保包含 `MUSIC_U=` 部分

### 可选环境变量

#### NODE_ENV
```
变量名: NODE_ENV
值: production
环境: Production
```

#### DEBUG
```
变量名: DEBUG
值: false
环境: Production
```

#### PORT
```
变量名: PORT
值: 3000
环境: All (Vercel会自动处理端口)
```

## 📁 项目结构调整

确保你的项目结构如下：
```
WyyTs-nodejs/
├── server.js              # 主服务器文件
├── lib/
│   └── netease-eapi.js    # API实现
├── public/                # 静态文件 (重要!)
│   ├── index.html
│   ├── css/
│   └── js/
├── package.json
├── vercel.json            # Vercel配置
└── .env.example           # 环境变量示例
```

## 🚨 重要注意事项

### 1. 静态文件路径
Vercel 会自动服务 `public` 目录下的静态文件，确保所有前端资源都在 `public` 目录中。

### 2. Serverless 函数限制
- 每个函数最大执行时间：30秒 (Hobby plan)
- 内存限制：1024MB (Hobby plan)
- 请求体大小限制：4.5MB

### 3. Cookie 安全
- 不要在代码中硬编码 Cookie
- 使用环境变量管理敏感信息
- 定期更新 Cookie

### 4. 域名配置
部署成功后，Vercel 会提供一个默认域名，如：
```
https://your-project-name.vercel.app
```

你也可以配置自定义域名。

## 🔍 部署后测试

### 1. 检查服务状态
访问：`https://your-domain.vercel.app/api/status`

### 2. 测试 Cookie 有效性
访问：`https://your-domain.vercel.app/api/cookie-check`

### 3. 测试单曲解析
使用前端界面或直接调用 API 测试功能。

## 🐛 常见问题

### 1. "functions 属性不能与 builds 属性同时使用"
**解决方案：**
- 使用默认的 `vercel.json` (只包含 builds)
- 或者将 `vercel-modern.json` 重命名为 `vercel.json` (只包含 functions)
- 不要同时使用两种配置

### 2. 部署失败
- 检查 `package.json` 中的依赖
- 确保 `vercel.json` 配置正确
- 查看部署日志排查错误

### 2. 环境变量不生效
- 确保变量名拼写正确
- 重新部署项目以应用新的环境变量
- 检查变量是否设置在正确的环境中

### 3. 静态文件 404
- 确保文件在 `public` 目录中
- 检查文件路径是否正确
- 清除浏览器缓存

### 4. API 超时
- 检查网络连接
- 优化 API 请求逻辑
- 考虑升级 Vercel 计划以获得更长的执行时间

## 📊 监控和日志

### 查看部署日志
```bash
vercel logs your-deployment-url
```

### 实时日志
```bash
vercel logs your-deployment-url --follow
```

### 性能监控
在 Vercel Dashboard 中查看：
- 函数执行时间
- 错误率
- 流量统计

---

部署完成后，你的网易云音乐解析器就可以在全球范围内访问了！🎉
