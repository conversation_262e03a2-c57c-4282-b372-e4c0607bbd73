{"name": "zip-stream", "version": "5.0.2", "description": "a streaming zip archive generator.", "homepage": "https://github.com/archiverjs/node-zip-stream", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/archiverjs/node-zip-stream.git"}, "bugs": {"url": "https://github.com/archiverjs/node-zip-stream/issues"}, "license": "MIT", "main": "index.js", "files": ["index.js"], "engines": {"node": ">= 12.0.0"}, "scripts": {"test": "mocha --reporter dot", "jsdoc": "jsdoc -c jsdoc.json README.md"}, "dependencies": {"archiver-utils": "^4.0.1", "compress-commons": "^5.0.1", "readable-stream": "^3.6.0"}, "devDependencies": {"archiver-jsdoc-theme": "1.1.3", "chai": "4.4.1", "jsdoc": "3.6.11", "minami": "1.2.3", "mkdirp": "3.0.1", "mocha": "9.2.2", "rimraf": "5.0.5"}, "keywords": ["archive", "stream", "zip-stream", "zip"]}