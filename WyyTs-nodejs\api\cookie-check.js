// Cookie检查 API
import dotenv from 'dotenv';
import { NeteaseEAPI } from '../lib/netease-eapi.js';

// 加载环境变量
dotenv.config();

const api = new NeteaseEAPI();

export default async function handler(req, res) {
  // 设置 CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    console.log('🍪 检测Cookie和会员状态');

    // 检查Cookie是否有效
    const cookieString = process.env.NETEASE_COOKIE;
    if (!cookieString || !cookieString.includes('MUSIC_U=')) {
      return res.json({
        status: 200,
        valid: false,
        error: '服务器未配置有效的NETEASE_COOKIE环境变量'
      });
    }

    const parsedCookies = api.parseCookie(cookieString);
    const cookies = api.createFullCookieObject(parsedCookies);

    const isValid = await api.testCookie(cookies);

    if (!isValid) {
      return res.json({
        status: 200,
        valid: false,
        error: 'Cookie已失效'
      });
    }

    // 获取用户信息和会员状态
    const userInfo = await api.getUserInfo(cookies);
    const profile = userInfo?.profile || {};
    const vipInfo = profile.vipType || 0;

    // 尝试多个可能的VIP过期时间字段
    let vipExpireTime = 0;
    if (profile.vipExpireTime && profile.vipExpireTime > 0) {
      vipExpireTime = profile.vipExpireTime;
    } else if (profile.viptypeVersion && profile.viptypeVersion > 0) {
      vipExpireTime = profile.viptypeVersion;
    }

    // 计算会员剩余天数
    const now = Date.now();
    let vipDaysLeft = 0;
    let vipExpired = true;

    if (vipExpireTime > 0) {
      vipDaysLeft = vipExpireTime > now ? Math.ceil((vipExpireTime - now) / (1000 * 60 * 60 * 24)) : 0;
      vipExpired = vipExpireTime <= now;
    }

    // 智能判断VIP状态
    const isVip = vipInfo > 0;
    if (!isVip) {
      // 非会员用户
    } else if (vipExpireTime === 0 || vipInfo >= 11) {
      // SVIP用户或长期有效
      vipExpired = false;
      vipDaysLeft = 999;
    }

    res.json({
      status: 200,
      valid: true,
      vipType: vipInfo,
      vipExpired: vipExpired,
      vipDaysLeft: vipDaysLeft,
      vipExpireTime: vipExpireTime > 0 ? new Date(vipExpireTime).toLocaleDateString('zh-CN') : '长期有效',
      isVip: isVip
    });

  } catch (error) {
    console.error('Cookie检测失败:', error);
    res.status(500).json({
      status: 500,
      valid: false,
      error: error.message
    });
  }
}
