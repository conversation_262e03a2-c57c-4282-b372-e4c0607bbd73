/**
 * UI控制模块 - 处理用户界面交互和显示
 */

class UIController {
    constructor() {
        this.currentTab = 'single';
        this.currentPlaylistSongs = [];
        this.resultContainer = null;
        
        this.init();
    }

    /**
     * 初始化UI控制器
     */
    init() {
        this.resultContainer = document.getElementById('result');
        this.setupEventListeners();
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 回车键支持
        document.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                const activeElement = document.activeElement;
                if (activeElement.id === 'singleInput') {
                    this.parseSingle();
                } else if (activeElement.id === 'batchInput') {
                    this.loadPlaylist();
                }
            }
        });

        // 设置API进度回调
        if (window.musicAPI) {
            window.musicAPI.setProgressCallback((progress) => {
                this.updateDownloadProgress(progress);
            });
        }
    }

    /**
     * 切换选项卡
     */
    showTab(tabName) {
        // 隐藏所有选项卡内容
        const tabContents = document.querySelectorAll('.tab-content');
        tabContents.forEach(content => {
            content.classList.remove('active');
        });

        // 移除所有按钮的active类
        const tabButtons = document.querySelectorAll('.tab-button');
        tabButtons.forEach(button => {
            button.classList.remove('active');
        });

        // 显示选中的选项卡
        const selectedTab = document.getElementById(tabName);
        if (selectedTab) {
            selectedTab.classList.add('active');
        }

        // 激活对应的按钮
        const selectedButton = document.querySelector(`[onclick="showTab('${tabName}')"]`);
        if (selectedButton) {
            selectedButton.classList.add('active');
        }

        this.currentTab = tabName;
        this.clearResult();
    }

    /**
     * 自动解析输入内容
     */
    autoParseInput(type) {
        const inputElement = document.getElementById(type === 'single' ? 'singleInput' : 'batchInput');
        const inputValue = inputElement.value.trim();

        if (!inputValue) {
            this.clearResult();
            return;
        }

        // 🔑 检查是否是落雪音乐token格式，如果是则不要自动解析
        if (inputValue.includes('###')) {
            console.log('🔑 检测到token格式，跳过自动解析以保留完整格式');
            this.showResult('info', `
                <h3>🔑 检测到落雪音乐Token格式</h3>
                <p>已识别为"我喜欢的歌单"格式，将使用用户token进行访问</p>
                <p style="margin-top: 10px;">点击"加载歌单"按钮继续</p>
            `);
            return;
        }

        const result = window.musicParser.parseInput(inputValue);

        if (result) {
            const formatted = window.musicParser.formatResult(result);
            if (formatted) {
                this.showParseResult(formatted);

                // 如果是批量下载，自动设置类型
                if (type === 'batch' && (formatted.type === 'playlist' || formatted.type === 'album')) {
                    const batchTypeSelect = document.getElementById('batchType');
                    batchTypeSelect.value = formatted.type;

                    // 只有在非token格式时才更新输入框为解析出的ID
                    inputElement.value = formatted.id;
                }
            }
        }
    }

    /**
     * 显示解析结果
     */
    showParseResult(result) {
        const typeName = window.musicParser.getTypeName(result.type);
        const extractedInfo = result.extractedInfo;
        
        let infoText = '';
        if (extractedInfo.songName) {
            infoText += `歌曲：${extractedInfo.songName}<br>`;
        }
        if (extractedInfo.artist) {
            infoText += `艺术家：${extractedInfo.artist}<br>`;
        }
        if (extractedInfo.albumName) {
            infoText += `专辑：${extractedInfo.albumName}<br>`;
        }
        if (extractedInfo.playlistName) {
            infoText += `歌单：${extractedInfo.playlistName}<br>`;
        }

        this.showResult('success', `
            <h3>🎯 自动识别成功</h3>
            <p>检测到${typeName}ID: ${result.id}</p>
            ${infoText ? `<div style="margin-top: 10px;">${infoText}</div>` : ''}
            <p style="margin-top: 10px;">点击相应按钮继续操作</p>
        `);
    }

    /**
     * 显示结果
     */
    showResult(type, content) {
        if (!this.resultContainer) return;

        this.resultContainer.innerHTML = `
            <div class="result ${type}">
                ${content}
            </div>
        `;

        // 滚动到结果区域
        this.resultContainer.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }

    /**
     * 清除结果
     */
    clearResult() {
        if (this.resultContainer) {
            this.resultContainer.innerHTML = '';
        }
    }

    /**
     * 显示歌单列表
     */
    showPlaylistSongs(songs, playlistName) {
        this.currentPlaylistSongs = songs;
        
        const playlistContainer = document.getElementById('playlistContainer');
        const playlistItems = document.getElementById('playlistItems');
        
        if (!playlistContainer || !playlistItems) return;

        // 生成歌曲列表HTML
        const songsHTML = songs.map((song, index) => `
            <div class="playlist-item" data-song-id="${song.id}">
                <div class="checkbox-container">
                    <input type="checkbox" class="playlist-checkbox" id="song-${index}" data-song-index="${index}">
                    <div class="checkbox-custom"></div>
                </div>
                <div class="playlist-info">
                    <div class="playlist-name">${song.name}</div>
                    <div class="playlist-artist">${song.artist}</div>
                </div>
            </div>
        `).join('');

        playlistItems.innerHTML = songsHTML;
        playlistContainer.style.display = 'block';

        // 添加点击事件
        playlistItems.addEventListener('click', (e) => {
            const playlistItem = e.target.closest('.playlist-item');
            if (playlistItem) {
                const checkbox = playlistItem.querySelector('.playlist-checkbox');
                if (e.target !== checkbox) {
                    checkbox.checked = !checkbox.checked;
                }
            }
        });

        this.showResult('success', `
            <h3>📋 歌单加载成功</h3>
            <p>歌单名称：${playlistName}</p>
            <p>共 ${songs.length} 首歌曲</p>
            <p style="margin-top: 10px;">请在下方选择要下载的歌曲</p>
        `);
    }

    /**
     * 全选/取消全选
     */
    toggleSelectAll() {
        const checkboxes = document.querySelectorAll('.playlist-checkbox');
        const allChecked = Array.from(checkboxes).every(cb => cb.checked);
        
        checkboxes.forEach(checkbox => {
            checkbox.checked = !allChecked;
        });
    }

    /**
     * 获取选中的歌曲
     */
    getSelectedSongs() {
        const checkboxes = document.querySelectorAll('.playlist-checkbox:checked');
        const selectedSongs = [];
        
        checkboxes.forEach(checkbox => {
            const index = parseInt(checkbox.dataset.songIndex);
            if (!isNaN(index) && this.currentPlaylistSongs[index]) {
                selectedSongs.push(this.currentPlaylistSongs[index]);
            }
        });
        
        return selectedSongs;
    }

    /**
     * 更新下载进度
     */
    updateDownloadProgress(progress) {
        const { current, total, song, success, failed } = progress;
        const percentage = Math.round((current / total) * 100);
        
        this.showResult('loading', `
            <h3>📥 正在下载 (${current}/${total})</h3>
            <div class="progress-container">
                <div class="progress-text">正在下载：${song.name} - ${song.artist}</div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${percentage}%"></div>
                    <div class="progress-percentage">${percentage}%</div>
                </div>
                <div class="progress-details">
                    <span>成功：${success}</span>
                    <span>失败：${failed}</span>
                    <span>剩余：${total - current}</span>
                </div>
            </div>
        `);
    }

    /**
     * 显示下载完成结果
     */
    showDownloadComplete(results) {
        const { successCount, failCount, results: downloadResults } = results;

        let resultHTML = `
            <h3>📥 批量下载完成</h3>
            <p>成功：${successCount} 首，失败：${failCount} 首</p>
        `;

        if (failCount > 0) {
            const failedSongs = downloadResults.filter(r => r.status === 'failed');
            resultHTML += `
                <div style="margin-top: 15px;">
                    <h4>失败的歌曲：</h4>
                    <ul style="margin-top: 10px;">
                        ${failedSongs.map(song => `
                            <li>${song.name} - ${song.artist} (${song.error})</li>
                        `).join('')}
                    </ul>
                </div>
            `;
        }

        this.showResult(failCount > 0 ? 'error' : 'success', resultHTML);
    }

    /**
     * 显示错误信息
     */
    showError(message) {
        this.showResult('error', `
            <h3>❌ 错误</h3>
            <p>${message}</p>
        `);
    }

    /**
     * 显示成功信息
     */
    showSuccess(content) {
        this.showResult('success', content);
    }

    /**
     * 显示加载状态
     */
    showLoading(message) {
        this.showResult('loading', `
            <h3>⏳ ${message}</h3>
            <div class="loading-spinner">
                <div class="spinner"></div>
            </div>
        `);
    }

    /**
     * 隐藏加载状态
     */
    hideLoading() {
        // 加载状态会被其他结果覆盖，这里不需要特殊处理
    }
}

// 创建全局UI控制器实例
window.uiController = new UIController();
