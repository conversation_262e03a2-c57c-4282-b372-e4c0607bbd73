{"author": "<PERSON><PERSON>", "name": "readdir-glob", "description": "Recursive fs.readdir with streaming API and glob filtering.", "version": "1.1.3", "homepage": "https://github.com/Yqnn/node-readdir-glob", "repository": {"type": "git", "url": "git://github.com/Yqnn/node-readdir-glob.git"}, "main": "index.js", "files": ["index.js"], "dependencies": {"minimatch": "^5.1.0"}, "devDependencies": {"jasmine": "^4.6.0", "mkdirp": "^2.1.6", "nyc": "^15.1.0", "rimraf": "^3.0.2", "tick": "0.0.6"}, "keywords": ["recursive", "fs", "stream", "streams", "readdir", "filesystem", "find", "filter", "glob"], "scripts": {"test": "npx nyc --reporter=lcov --reporter=text-summary --reporter=html jasmine --config=jasmine.json", "test-regen": "TEST_REGEN=1 jasmine --config=jasmine.json", "bench": "bash scripts/benchmark.sh", "profile": "bash scripts/profile.sh && cat profile.txt"}, "license": "Apache-2.0"}