// 批量下载 API
require('dotenv').config();
const { NeteaseEAPI } = require('../lib/netease-eapi-simple.js');

const api = new NeteaseEAPI();

module.exports = async function handler(req, res) {
  // 设置 CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { songs, level = 'lossless' } = req.body;

    console.log(`🎵 收到批量下载请求: ${songs?.length || 0} 首歌曲, 音质: ${level}`);

    if (!songs || !Array.isArray(songs) || songs.length === 0) {
      return res.status(400).json({
        status: 400,
        error: '必须提供歌曲列表'
      });
    }

    // Serverless 环境限制，最多处理30首歌曲
    if (songs.length > 30) {
      return res.status(400).json({
        status: 400,
        error: `批量下载最多支持30首歌曲，您选择了${songs.length}首。请减少选择数量。`
      });
    }

    // 检查Cookie是否有效
    const cookieString = process.env.NETEASE_COOKIE;
    if (!cookieString || !cookieString.includes('MUSIC_U=')) {
      return res.status(400).json({
        status: 400,
        error: '服务器未配置有效的NETEASE_COOKIE环境变量，请联系管理员设置'
      });
    }

    const parsedCookies = api.parseCookie(cookieString);
    const cookies = api.createFullCookieObject(parsedCookies);

    const results = [];
    let successCount = 0;
    let failedCount = 0;

    // 处理每首歌曲
    for (let i = 0; i < songs.length; i++) {
      const song = songs[i];
      
      try {
        console.log(`🎵 处理歌曲 ${i + 1}/${songs.length}: ${song.name} - ${song.artist}`);

        const result = await api.url_v1(song.id.toString(), level, cookies);

        if (result && result.data && result.data.length > 0) {
          const songData = result.data[0];

          if (songData.url) {
            const downloadResult = {
              id: song.id,
              name: song.name,
              artist: song.artist,
              success: true,
              url: songData.url.replace('http://', 'https://'),
              size: songData.size,
              br: songData.br,
              level: api.getQualityName(level),
              fileSize: api.formatFileSize(songData.size),
              isLossless: api.isLosslessQuality(songData.br)
            };

            successCount++;
            results.push(downloadResult);
          } else {
            throw new Error(`无法获取下载链接 (code: ${songData.code})`);
          }
        } else {
          throw new Error('无响应数据');
        }

        // 添加延迟避免请求过快
        if (i < songs.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }

      } catch (error) {
        const downloadResult = {
          id: song.id,
          name: song.name,
          artist: song.artist,
          success: false,
          error: error.message
        };

        failedCount++;
        results.push(downloadResult);
      }
    }

    console.log(`📥 批量下载完成: 成功 ${successCount}/${songs.length}, 失败 ${failedCount}/${songs.length}`);

    // 返回结果
    res.json({
      status: 200,
      downloadType: 'individual',
      total: songs.length,
      success: successCount,
      failed: failedCount,
      results,
      note: songs.length > 20 ? '⚠️ Serverless环境限制，建议分批下载以获得更好的性能' : null,
      quality: level,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('批量下载错误:', error);
    res.status(500).json({
      status: 500,
      error: '服务器内部错误',
      details: error.message
    });
  }
};
